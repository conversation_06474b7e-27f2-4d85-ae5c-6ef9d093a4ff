/*
 * 此处填写用户程序业务逻辑描述
 */

#include "types.h"
#include "gic.h"        /* 中断控制功能 */
#include "bsp_print.h"  /* 串口输出功能 */
#include <stdbool.h>

extern int irq_count;

#define SIZE (256)  // 单位：MB
#define START_ADDR (volatile uint32_t*)0x00000000

/* 辅助函数 - 打印内存空间 */
void output_memory_hex() {
    volatile uint32_t* ptr = START_ADDR;
    const uint32_t total_words = (SIZE * 1024 * 1024) / 4; // 计算总的32位字数

    bool is_skipping = false;      // 状态标志：是否正处于省略打印模式
    uint32_t skipped_value = 0;    // 在省略模式下，记录被省略的重复值

    // 外层循环，每次处理一行（10个字）
    for (uint32_t i = 0; i < total_words; i += 10) {
        
        // 检查当前行是否所有数据都相同
        bool all_same_in_line = true;
        uint32_t first_word = ptr[i];
        uint32_t words_in_line = (total_words - i < 10) ? (total_words - i) : 10;

        // 从第二个字开始比较
        for (uint32_t j = 1; j < words_in_line; ++j) {
            if (ptr[i + j] != first_word) {
                all_same_in_line = false;
                break;
            }
        }

        // 根据检查结果和当前状态，决定如何打印
        if (all_same_in_line) {
            if (is_skipping && first_word == skipped_value) {
                // 情况A: 已经处于省略模式，且当前行的重复值和被省略的值相同
                // -> 什么都不做，直接跳过这一整行
                continue;
            } else {
                // 情况B: 发现了新的重复数据行（之前没在省略，或重复值变了）
                // -> 打印省略信息，并进入/更新省略状态
                print2("[0x%x]: ... (Content repeating with value: 0x%x)\n", (unsigned int)(ptr + i), first_word);
                is_skipping = true;
                skipped_value = first_word;
            }
        } else {
            // 情况C: 当前行数据不统一
            // -> 必须退出省略模式，并正常打印这一行
            is_skipping = false;
            
            // 打印行起始地址
            print2("[0x%x]:", (unsigned int)(ptr + i));
            
            // 打印该行所有数据
            for (uint32_t j = 0; j < words_in_line; ++j) {
                print2(" 0x%x", ptr[i + j]);
            }
            print2("\n");
        }
    }
}

/* 辅助函数 - 查询值 */
void find_value_in_memory(uint32_t target) {
    volatile uint32_t* ptr = START_ADDR;
    uint32_t total = (SIZE * 1024 * 1024) / 4;
    uint32_t i;

    for (i = 0; i < total; ++i) {
        if (ptr[i] == target) {
            print2("Found at address: 0x%x\n", (unsigned int)(ptr + i));
        }
    }
}

void norflash_read_test(void) {
    print2("=================== norflash_read_test ===================\n");
    output_memory_hex();
    
    *(volatile uint32_t*)0x04000000 = 0x12345678;
    
    output_memory_hex();
    print2("=================== end ===================\n");
}

void rom0_write_test(void) {
    int info = 0;
    print2("=================== norflash_write_test ===================\n");
    print2("Before initialization:\n");
    output_memory_hex();

    /* 片擦除 */
    *((volatile unsigned char *)(0x00000000 + 0x555)) = 0xAA;
	*((volatile unsigned char *)(0x00000000 + 0x2AA)) = 0x55;
	*((volatile unsigned char *)(0x00000000 + 0x555)) = 0x80;
	*((volatile unsigned char *)(0x00000000 + 0x555)) = 0xAA;
	*((volatile unsigned char *)(0x00000000 + 0x2AA)) = 0x55;
	*((volatile unsigned char *)(0x00000000 + 0x555)) = 0x10;

    int a = 0;
    for(int i = 0; i < 100000000; i++) {
        a = ((a + i - i/2) * 3) % 10; 
    }
    (void)a;

    print2("After initialization:\n");
    output_memory_hex();
    
    *((volatile unsigned char *)(0x00000000 + 0x555)) = 0xF0;
    /* 解锁序列 */
    *((volatile unsigned char *)(0x00000000 + 0x555)) = 0xAA;
    *((volatile unsigned char *)(0x00000000 + 0x2AA)) = 0x55;
    *((volatile unsigned char *)(0x00000000 + 0x555)) = 0xA0;

    /* 写入值 */
    *((volatile unsigned char *)(0x04000000)) = 0x78;
    *((volatile unsigned char *)(0x04000001)) = 0x56;
    *((volatile unsigned char *)(0x04000002)) = 0x34;
    *((volatile unsigned char *)(0x04000003)) = 0x12;

    print2("\nAfter write:\n");
    output_memory_hex();

    uint32_t rdata = *((volatile unsigned int *)(0x04000000));
    print2("Read data: 0x%x\n", rdata);
    print2("=================== end ===================\n");
}

void rom0_id_test(void) {
    int info = 0;
    print2("=================== rom0_id_test ===================\n");
    /* 解锁序列 */
    *((volatile unsigned char *)(0x00000000 + 0x555)) = 0xAA;
    *((volatile unsigned char *)(0x00000000 + 0x2AA)) = 0x55;
    *((volatile unsigned char *)(0x00000000 + 0x555)) = 0x90;
	
   	info = *((volatile unsigned char *)(0x00000000 + 0x00)); 
	print2("Manifacture ID = 0x%x\n", info);
   	info = *((volatile unsigned char *)(0x00000000 + 0x01)); 
	print2("Device ID1 = 0x%x\n", info);
   	info = *((volatile unsigned char *)(0x00000000 + (0x0E))); 
	print2("Device ID2 = 0x%x\n", info);
    print2("=================== end ===================\n");
}

void rom1_id_test(void) {
    int info = 0;
    print2("=================== rom1_id_test ===================\n");
    /* 解锁序列 */
    *((volatile unsigned char *)(0x04000000 + 0xAAA)) = 0xAA;
    *((volatile unsigned char *)(0x04000000 + 0x555)) = 0x55;
    *((volatile unsigned char *)(0x04000000 + 0xAAA)) = 0x90;
	
   	info = *((volatile unsigned char *)(0x04000000 + 0x00)); 
	print2("Manifacture ID = 0x%x\n", info);
   	info = *((volatile unsigned char *)(0x04000000 + 0x02)); 
	print2("Device ID1 = 0x%x\n", info);
   	info = *((volatile unsigned char *)(0x04000000 + (0x1c))); 
	print2("Device ID2 = 0x%x\n", info);
    print2("=================== end ===================\n");
}

void rom2_id_test(void) {
    int info = 0;
    print2("=================== rom2_id_test ===================\n");
    /* 解锁序列 */
    *((volatile unsigned int *)(0x08000000 + (0x555 << 2))) = 0xAA;
    *((volatile unsigned int *)(0x08000000 + (0x2AA << 2))) = 0x55;
    *((volatile unsigned int *)(0x08000000 + (0x555 << 2))) = 0x90;
	
   	info = *((volatile unsigned int *)(0x08000000 + 0x00)); 
	print2("Manifacture ID = 0x%x\n", info);
   	info = *((volatile unsigned int *)(0x08000000 + (0x01 << 2))); 
	print2("Device ID1 = 0x%x\n", info);
   	info = *((volatile unsigned int *)(0x08000000 + (0x0E << 2))); 
	print2("Device ID2 = 0x%x\n", info);
    print2("=================== end ===================\n");
}

void rom3_id_test(void) {
    int info = 0;
    print2("=================== rom3_id_test ===================\n");
    /* 解锁序列 */
    *((volatile unsigned short *)(0x0C000000 + (0x555 << 1))) = 0xAA;
    *((volatile unsigned short *)(0x0C000000 + (0x2AA << 1))) = 0x55;
    *((volatile unsigned short *)(0x0C000000 + (0x555 << 1))) = 0x90;
	
   	info = *((volatile unsigned short *)(0x0C000000 + 0x00)); 
	print2("Manifacture ID = 0x%x\n", info);
   	info = *((volatile unsigned short *)(0x0C000000 + (0x01 << 1))); 
	print2("Device ID1 = 0x%x\n", info);
   	info = *((volatile unsigned short *)(0x0C000000 + (0x0E << 1))); 
	print2("Device ID2 = 0x%x\n", info);
    print2("=================== end ===================\n");
}

void print2_hex(unsigned int val) {
    // 缓冲区足以容纳 "0x" 前缀, 8个16进制数字, 以及一个空终止符 '\0'
    char hex_str[11];
    const char *hex_digits = "0123456789ABCDEF";

    hex_str[0] = '0';
    hex_str[1] = 'x';
    hex_str[10] = '\0'; // 在字符串末尾设置空终止符

    // 循环8次，对应8个16进制数字
    for (int i = 0; i < 8; i++) {
        // 计算当前要提取的“四位组”(nibble)的位移量。
        // i=0时, 提取最高位的四位组 (位移28)
        // i=7时, 提取最低位的四位组 (位移0)
        int shift = 28 - (i * 4);
        
        // 1. 右移整数，将目标四位组移动到最低位
        // 2. 使用 & 0xF 操作隔离出这个四位组的值 (0-15)
        unsigned int nibble = (val >> shift) & 0xF;
        
        // 从查找表中获取对应的16进制字符，并放入字符串缓冲区。
        // 16进制数字从缓冲区的第2个索引开始存放。
        hex_str[i + 2] = hex_digits[nibble];
    }

    // 一次性打印整个构建好的字符串
    print2(hex_str);
}

int main(void)
{
    /* GIC 初始化 */
    // gic_init();
    
    /* 业务代码开始 */
    
    print2("\n--- Detailed Assembly Test Start ---\n");

    // 准备用于加载的内存
    volatile unsigned int dummy_memory[4] = { 0xDEADBEEF, 0xCAFECAFE, 0x12345678, 0x87654321 };
    volatile unsigned int *p_mem = dummy_memory;
    volatile unsigned int original_cpsr, svc_cpsr;

    // --- 步骤 1: 读取当前CPSR ---
    print2("Step 1: Reading current CPSR...\n");
    __asm__ __volatile__ (
        "mrs %[cpsr_val], cpsr\n\t"
        : [cpsr_val] "=r" (original_cpsr)
    );
    print2("  Original CPSR is: ");
    print2_hex(original_cpsr); // 打印原始CPSR的值
    print2("\n");

    // --- 步骤 2: 准备切换到SVC模式 ---
    print2("Step 2: Preparing to switch to SVC mode...\n");
    svc_cpsr = (original_cpsr & ~0x1F) | 0x13; // 清除模式位，设置为SVC (0b10011)
    print2("  Target SVC CPSR will be: ");
    print2_hex(svc_cpsr);
    print2("\n");

    // --- 步骤 3: 正式切换到SVC模式 ---
    print2("Step 3: Executing MSR to switch to SVC mode...\n");
    __asm__ __volatile__ (
        "msr cpsr_c, %[new_cpsr]\n\t"
        :
        : [new_cpsr] "r" (svc_cpsr)
    );
    print2("  Successfully switched to SVC mode.\n");

    // --- 步骤 4: 验证当前模式 (在SVC模式下读取CPSR) ---
    unsigned int current_cpsr_in_svc;
    print2("Step 4: Verifying current mode is SVC...\n");
    __asm__ __volatile__ (
        "mrs %[cpsr_val], cpsr\n\t"
        : [cpsr_val] "=r" (current_cpsr_in_svc)
    );
    print2("  CPSR read from within SVC mode: ");
    print2_hex(current_cpsr_in_svc);
    print2("\n");
    if ((current_cpsr_in_svc & 0x1F) == 0x13) {
        print2("  Verification PASSED. Current mode is SVC.\n");
    } else {
        print2("  Verification FAILED. Current mode is NOT SVC. Aborting test.\n");
        goto test_end; // 如果模式不对，直接跳到结尾
    }

    // --- 步骤 5: 准备执行核心测试指令 ---
    print2("Step 5: Preparing to execute 'ldm r0!, {lr}^'...\n");
    print2("  Address to load from (r0) will be: ");
    print2_hex((unsigned int)p_mem);
    print2("\n");
    print2("  Value at that address is: ");
    print2_hex(*p_mem);
    print2("\n");
    print2("  Connect GDB now! Breakpoint at Undefined Instruction vector.\n");
    print2("  >>> EXECUTING THE TARGET INSTRUCTION NOW <<<\n");

    // --- 步骤 6: 执行核心测试指令 ---
    __asm__ __volatile__ (
        "mov r0, %[ptr]\n\t"
        "ldm r0!, {lr}^"
        : [ptr] "+r" (p_mem)
        :
        : "r0", "lr", "memory"
    );

    // 如果程序能执行到这里，说明没有触发异常
    print2("\n  >>> INSTRUCTION EXECUTED SUCCESSFULLY! <<<\n");
    print2("  No immediate exception was triggered.\n");
    print2("  The value of p_mem is now: ");
    print2_hex((unsigned int)p_mem); // 检查r0是否自增了
    print2("\n");


test_end:
    // --- 步骤 7: 恢复原始模式 ---
    print2("Step 7: Restoring original CPSR...\n");
    __asm__ __volatile__ (
        "msr cpsr_c, %[orig_cpsr]\n\t"
        :
        : [orig_cpsr] "r" (original_cpsr)
    );

    print2("--- Detailed Assembly Test End ---\n");


    /* 业务代码结束 */

    return 0;
}
