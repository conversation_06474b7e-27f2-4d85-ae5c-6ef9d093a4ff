#include "emif.h"
#include "gic.h"
#include "types.h"
#include "uart.h"

#if(GFS_EMIF_SRAM_AVAILABLE == 1)
static void Bsp_EmifRamInit(void);
#endif
static void Bsp_EmifRomInit(void);
static void Bsp_EmifIoInit(void);

void emif_se_isr()
{
    uart_puts("emif_se_isr has been raised\n");
	U32 seAddr = BSP_EMIF_SingleErr_GetAddr();
	//TODO

    *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EMIF_INT_CLEAR_REG) = 0x00000000; //清除中断

}

void emif_de_isr()
{
    uart_puts("emif_de_isr has been raised\n");
	U32 deAddr = BSP_EMIF_MultiErr_GetAddr();
	//TODO

    *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EMIF_INT_CLEAR_REG) = 0x00000000; //清除中断

}

void emif_io_isr()
{
    uart_puts("emif_io_isr has been raised\n");
    *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EMIF_INT_CLEAR_REG) = 0x00000000; //清除中断

}

static void Bsp_EmifRamInit(void)
{
	U32 temp;

	/* RAM空间配置 */
	*(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + WRMEM_CONFIG_REG) = BSP_EMIF_RAM_WRMEM_CONFIG_REG;

	/*RAM 等待时序配置*/
	*(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + WR_WS_CFG_REG) = BSP_EMIF_RAM_WR_WS_CFG_REG;

	/*RAM EDAC配置*/
	temp = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG);
	//bit4 7 先置0
	temp = temp & (U32)(~((1<<4) | (1<<7)));
	temp = temp |(BSP_EMIF_RAMEDAC_EN<<4) | (BSP_EMIF_RAM_WR_WRITE_BACK_EN<<7);
	*(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG) = temp;
}

static void Bsp_EmifRomInit(void)
{
	U32 temp;

	/* ROM空间配置 */
	*(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + ORMEM_CONFIG_REG) = BSP_EMIF_ROM_ORMEM_config_reg;

	/*ROM 等待时序配置*/
	*(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + OR_WS_CFG_REG) = BSP_EMIF_ROM_OR_WS_CFG_REG;

	/*ROM EDAC配置*/
	temp = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG);
	//bit0 3 先置0
	temp = temp & (U32)(~((1<<0) | (1<<3)));
	temp = temp | BSP_EMIF_ROMEDAC_EN | (BSP_EMIF_ROM_OR_WRITE_BACK_EN<<3);

	*(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG) = temp;
}

static void Bsp_EmifIoInit(void)
{
	/* IO空间配置 */
	*(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + IO_CONFIG_REG) = BSP_EMIF_IO_config_reg;

#if (BSP_EMIF_IO_rdy == 1)
	/*IO等待Ready信号到来的超时时间设置*/
	*(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + IO_RDY_TIMEOUT_REG) = BSP_EMIF_IO_Rdy_Timeout_reg;
#endif

	/*IO等待时序配置*/
	*(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + IO_WS_CFG_REG) = BSP_EMIF_IO_WS_CFG_REG;
}


void Bsp_EmifInit(void)
{
	U32 temp;
	/*emif ram 初始化*/
	#if(GFS_EMIF_SRAM_AVAILABLE == 1)
	Bsp_EmifRamInit();
	#endif

	/*emif rom 初始化*/
	Bsp_EmifRomInit();

	/*emif IO 初始化*/
	Bsp_EmifIoInit();
	//显式初始化 bit8，发生双错报告数据错误中断
	temp = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG);
	//bit8 设为0
	temp=temp & 0xfffffeff;
	temp= temp | (U32)(BSP_EMIF_DOUBLE_ERR_EN<<8);
	*(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG)=temp;
	return;
}

/*最近发生单错的访问字地址*/
U32 BSP_EMIF_SingleErr_GetAddr(void)
{
	return (*(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + CERR_ADDR_REG));
}

/*最近发生双错的访问字地址*/
U32 BSP_EMIF_MultiErr_GetAddr(void)
{
	return (*(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + DERR_ADDR_REG));
}

/*使能/禁止RAM区EDAC*/
void Bsp_SetEmifRamEdac(U32 set)
{
	U32 temp;

	if(BSP_ENABLE == set)
	{
		temp = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG);
		temp = temp | 0x10;
		*(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG) = temp;
	}
	else
	{
		temp = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG);
		temp = temp & 0x1ef;
		*(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG) = temp;
	}
}

/*读旁路使能/禁止*/
void Bsp_EmifSetRamEdacReadByPass(U32 set)
{
	U32 temp;

	if(BSP_ENABLE == set)
	{
		temp = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG);
		temp = temp |0x20;
		*(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG) = temp;
	}
	else
	{
		temp = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG);
		temp = temp &0x1df;
		*(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG) = temp;
	}
}

/*写旁路使能/禁止*/
void Bsp_EmifSetRamEdacWriteByPass(U32 set)
{
	U32 temp;

	if(BSP_ENABLE == set)
	{
		temp = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG);
		temp = temp |0x40;
		*(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG) = temp;
	}
	else
	{
		temp = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG);
		temp = temp &0x1bf;
		*(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG) = temp;
	}
}

/*使能ROM区EDAC*/
void Bsp_SetRomEdac(U32 set)
{
	U32 temp;

	if(BSP_ENABLE == set)
	{
		temp = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG);
		temp = temp | 0x1;
		*(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG) = temp;
	}
	else
	{
		temp = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG);
		temp = temp & 0x1fe;
		*(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG) = temp;
	}
}

/*获取ROM区EDAC使能状态*/
U32 Bsp_GetRomEdac(void)
{
	U32 temp = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG);

	if(BSP_ENABLE == (temp&0x1))
	{
		return SYSTRUE;
	}
	else
	{
		return SYSFALSE;
	}
}

/*EMIF错误中断清零*/
I32 Bsp_EmifClearIntStatus(U32 isrID)
{
	switch(isrID)
	{
		case BSP_EMIF_SEU_IRQID:
			*(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EMIF_INT_CLEAR_REG) = 0x1;
			break;
		case BSP_EMIF_DOUBLE_ERR_INT:
			*(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EMIF_INT_CLEAR_REG) = 0x2;
			break;
		case BSP_EMIF_WRITE_TO_FORBIDEN_AREA_ERR_IRQID:
			*(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EMIF_INT_CLEAR_REG) = 0x4;
			break;
		case BSP_EMIF_IO_RDY_BUS_TIMEOUT_ERR_IRQID:
			*(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EMIF_INT_CLEAR_REG) = 0x8;
			break;
		default:
			return -1;
	}
	return 0;
}

/*设置ROM区位宽*/
void Bsp_SetRomWidth(U32 width)
{
	V_U32 *cpu_mec = (V_U32 *)(BSP_EMIF_REG_BASE_ADDR + ORMEM_CONFIG_REG);
	//把14位、15位清0
	U32 temp = cpu_mec[0x0 / 4] & 0xffff3fff;
	if (EMIF_PROM_WIDTH_8 == width)
	{
		cpu_mec[0x0 / 4] = temp;
	}
	else if (EMIF_PROM_WIDTH_16 == width)
	{
		cpu_mec[0x0 / 4] = temp | 0x4000;
	}
	else
	{
		cpu_mec[0x0 / 4] = temp | 0xc000;
	}
}

/*获取ROM区位宽*/
U32 Bsp_GetRomWidth(void)
{
	V_U32 *cpu_mec = (V_U32 *)(BSP_EMIF_REG_BASE_ADDR + ORMEM_CONFIG_REG);
	//取14、15位
	U32 width = (cpu_mec[0x0 / 4]>>14) & 0x3;
	if (BSP_EMIF_DATA8 == width)
	{
		return EMIF_PROM_WIDTH_8;
	}
	else if (BSP_EMIF_DATA16 == width)
	{
		return EMIF_PROM_WIDTH_16;
	}
	else
	{
		return EMIF_PROM_WIDTH_32;
	}
}
