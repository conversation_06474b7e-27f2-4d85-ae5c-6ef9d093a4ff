#include <stdint.h>

/* GPIO base address - replace with actual hardware address */
#define GPIO_BASE_ADDRESS 0xA00D0000

/* Register offsets */
#define GPIO_SWPORTA_DR_OFFSET 0x00
#define GPIO_SWPORTA_DDR_OFFSET 0x04
#define GPIO_SWPORTA_CTL_OFFSET 0x08
#define GPIO_SWPORTB_DR_OFFSET 0x0C
#define GPIO_SWPORTB_DDR_OFFSET 0x10
#define GPIO_SWPORTB_CTL_OFFSET 0x14
#define GPIO_SWPORTC_DR_OFFSET 0x18
#define GPIO_SWPORTC_DDR_OFFSET 0x1C
#define GPIO_SWPORTC_CTL_OFFSET 0x20
#define GPIO_SWPORTD_DR_OFFSET 0x24
#define GPIO_SWPORTD_DDR_OFFSET 0x28
#define GPIO_SWPORTD_CTL_OFFSET 0x2C
#define GPIO_INTEN_OFFSET 0x30
#define GPIO_INTMASK_OFFSET 0x34
#define GPIO_INTTYPE_LEVEL_OFFSET 0x38
#define GPIO_INT_POLARITY_OFFSET 0x3C
#define GPIO_INTSTATUS_OFFSET 0x40
#define GPIO_RAW_INTSTATUS_OFFSET 0x44
#define GPIO_DEBOUNCE_OFFSET 0x48
#define GPIO_PORTA_EOI_OFFSET 0x4C
#define GPIO_EXT_PORTA_OFFSET 0x50
#define GPIO_EXT_PORTB_OFFSET 0x54
#define GPIO_EXT_PORTC_OFFSET 0x58
#define GPIO_EXT_PORTD_OFFSET 0x5C
#define GPIO_LS_SYNC_OFFSET 0x60
#define GPIO_ID_CODE_OFFSET 0x64
#define GPIO_INT_BOTHEDGE_OFFSET 0x68
#define GPIO_VER_ID_CODE_OFFSET 0x6C
#define GPIO_CONFIG_REG2_OFFSET 0x70
#define GPIO_CONFIG_REG1_OFFSET 0x74

/* Get GPIO base address */
volatile uint32_t* gpio_get_base(void) {
    return (volatile uint32_t*)GPIO_BASE_ADDRESS;
}

/* Port-specific functions */
/*===========================Port ABCD Read Write Start===================================*/
/* Port A */
uint32_t gpio_porta_dr_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_SWPORTA_DR_OFFSET / sizeof(uint32_t)));
}

void gpio_porta_dr_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_SWPORTA_DR_OFFSET / sizeof(uint32_t))) = value;
}

uint32_t gpio_porta_ddr_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_SWPORTA_DDR_OFFSET / sizeof(uint32_t)));
}

void gpio_porta_ddr_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_SWPORTA_DDR_OFFSET / sizeof(uint32_t))) = value;
}

uint32_t gpio_porta_ctl_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_SWPORTA_CTL_OFFSET / sizeof(uint32_t)));
}

void gpio_porta_ctl_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_SWPORTA_CTL_OFFSET / sizeof(uint32_t))) = value;
}

/* Port B */
uint32_t gpio_portb_dr_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_SWPORTB_DR_OFFSET / sizeof(uint32_t)));
}

void gpio_portb_dr_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_SWPORTB_DR_OFFSET / sizeof(uint32_t))) = value;
}

uint32_t gpio_portb_ddr_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_SWPORTB_DDR_OFFSET / sizeof(uint32_t)));
}

void gpio_portb_ddr_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_SWPORTB_DDR_OFFSET / sizeof(uint32_t))) = value;
}

uint32_t gpio_portb_ctl_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_SWPORTB_CTL_OFFSET / sizeof(uint32_t)));
}

void gpio_portb_ctl_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_SWPORTB_CTL_OFFSET / sizeof(uint32_t))) = value;
}

/* Port C */
uint32_t gpio_portc_dr_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_SWPORTC_DR_OFFSET / sizeof(uint32_t)));
}

void gpio_portc_dr_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_SWPORTC_DR_OFFSET / sizeof(uint32_t))) = value;
}

uint32_t gpio_portc_ddr_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_SWPORTC_DDR_OFFSET / sizeof(uint32_t)));
}

void gpio_portc_ddr_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_SWPORTC_DDR_OFFSET / sizeof(uint32_t))) = value;
}

uint32_t gpio_portc_ctl_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_SWPORTC_CTL_OFFSET / sizeof(uint32_t)));
}

void gpio_portc_ctl_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_SWPORTC_CTL_OFFSET / sizeof(uint32_t))) = value;
}

/* Port D */
uint32_t gpio_portd_dr_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_SWPORTD_DR_OFFSET / sizeof(uint32_t)));
}

void gpio_portd_dr_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_SWPORTD_DR_OFFSET / sizeof(uint32_t))) = value;
}

uint32_t gpio_portd_ddr_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_SWPORTD_DDR_OFFSET / sizeof(uint32_t)));
}

void gpio_portd_ddr_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_SWPORTD_DDR_OFFSET / sizeof(uint32_t))) = value;
}

uint32_t gpio_portd_ctl_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_SWPORTD_CTL_OFFSET / sizeof(uint32_t)));
}

void gpio_portd_ctl_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_SWPORTD_CTL_OFFSET / sizeof(uint32_t))) = value;
}

/*===========================函数名形式Port-ABCD Read Write End===================================*/


/*===========================参数形式Port-ABCD Read Write Start===================================*/
/* Generic port functions */
uint32_t gpio_port_dr_read(uint8_t port) {
    volatile uint32_t* base = gpio_get_base();
    uint32_t offset;
    switch (port) {
        case 0: offset = GPIO_SWPORTA_DR_OFFSET; break;
        case 1: offset = GPIO_SWPORTB_DR_OFFSET; break;
        case 2: offset = GPIO_SWPORTC_DR_OFFSET; break;
        case 3: offset = GPIO_SWPORTD_DR_OFFSET; break;
        default: return 0; /* Invalid port */
    }
    return *(base + (offset / sizeof(uint32_t)));
}

void gpio_port_dr_write(uint8_t port, uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    uint32_t offset;
    switch (port) {
        case 0: offset = GPIO_SWPORTA_DR_OFFSET; break;
        case 1: offset = GPIO_SWPORTB_DR_OFFSET; break;
        case 2: offset = GPIO_SWPORTC_DR_OFFSET; break;
        case 3: offset = GPIO_SWPORTD_DR_OFFSET; break;
        default: return; /* Invalid port */
    }
    *(base + (offset / sizeof(uint32_t))) = value;
}

uint32_t gpio_port_ddr_read(uint8_t port) {
    volatile uint32_t* base = gpio_get_base();
    uint32_t offset;
    switch (port) {
        case 0: offset = GPIO_SWPORTA_DDR_OFFSET; break;
        case 1: offset = GPIO_SWPORTB_DDR_OFFSET; break;
        case 2: offset = GPIO_SWPORTC_DDR_OFFSET; break;
        case 3: offset = GPIO_SWPORTD_DDR_OFFSET; break;
        default: return 0; /* Invalid port */
    }
    return *(base + (offset / sizeof(uint32_t)));
}

void gpio_port_ddr_write(uint8_t port, uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    uint32_t offset;
    switch (port) {
        case 0: offset = GPIO_SWPORTA_DDR_OFFSET; break;
        case 1: offset = GPIO_SWPORTB_DDR_OFFSET; break;
        case 2: offset = GPIO_SWPORTC_DDR_OFFSET; break;
        case 3: offset = GPIO_SWPORTD_DDR_OFFSET; break;
        default: return; /* Invalid port */
    }
    *(base + (offset / sizeof(uint32_t))) = value;
}

uint32_t gpio_port_ctl_read(uint8_t port) {
    volatile uint32_t* base = gpio_get_base();
    uint32_t offset;
    switch (port) {
        case 0: offset = GPIO_SWPORTA_CTL_OFFSET; break;
        case 1: offset = GPIO_SWPORTB_CTL_OFFSET; break;
        case 2: offset = GPIO_SWPORTC_CTL_OFFSET; break;
        case 3: offset = GPIO_SWPORTD_CTL_OFFSET; break;
        default: return 0; /* Invalid port */
    }
    return *(base + (offset / sizeof(uint32_t)));
}

void gpio_port_ctl_write(uint8_t port, uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    uint32_t offset;
    switch (port) {
        case 0: offset = GPIO_SWPORTA_CTL_OFFSET; break;
        case 1: offset = GPIO_SWPORTB_CTL_OFFSET; break;
        case 2: offset = GPIO_SWPORTC_CTL_OFFSET; break;
        case 3: offset = GPIO_SWPORTD_CTL_OFFSET; break;
        default: return; /* Invalid port */
    }
    *(base + (offset / sizeof(uint32_t))) = value;
}
/*===========================参数形式Port-ABCD Read Write End===================================*/



/* Other GPIO registers */

/* Interrupt enable register */
uint32_t gpio_inten_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_INTEN_OFFSET / sizeof(uint32_t)));
}

void gpio_inten_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_INTEN_OFFSET / sizeof(uint32_t))) = value;
}

/* Interrupt mask register */
uint32_t gpio_intmask_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_INTMASK_OFFSET / sizeof(uint32_t)));
}

void gpio_intmask_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_INTMASK_OFFSET / sizeof(uint32_t))) = value;
}

/* Interrupt level register: 0 = level, 1 = edge */
uint32_t gpio_inttype_level_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_INTTYPE_LEVEL_OFFSET / sizeof(uint32_t)));
}

void gpio_inttype_level_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_INTTYPE_LEVEL_OFFSET / sizeof(uint32_t))) = value;
}

/* Interrupt polarity register */
uint32_t gpio_int_polarity_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_INT_POLARITY_OFFSET / sizeof(uint32_t)));
}

void gpio_int_polarity_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_INT_POLARITY_OFFSET / sizeof(uint32_t))) = value;
}

/* Interrupt status register */
uint32_t gpio_intstatus_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_INTSTATUS_OFFSET / sizeof(uint32_t)));
}

void gpio_intstatus_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_INTSTATUS_OFFSET / sizeof(uint32_t))) = value;
}

/* Raw interrupt status register */
uint32_t gpio_raw_intstatus_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_RAW_INTSTATUS_OFFSET / sizeof(uint32_t)));
}

void gpio_raw_intstatus_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_RAW_INTSTATUS_OFFSET / sizeof(uint32_t))) = value;
}

/* Debounce enable register */
uint32_t gpio_debounce_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_DEBOUNCE_OFFSET / sizeof(uint32_t)));
}

void gpio_debounce_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_DEBOUNCE_OFFSET / sizeof(uint32_t))) = value;
}

/* Port A clear interrupt register */
uint32_t gpio_porta_eoi_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_PORTA_EOI_OFFSET / sizeof(uint32_t)));
}

void gpio_porta_eoi_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_PORTA_EOI_OFFSET / sizeof(uint32_t))) = value;
}

/* External port register */
uint32_t gpio_ext_port_read(uint8_t port) {
    volatile uint32_t* base = gpio_get_base();
    uint32_t offset;
    switch (port) {
        case 0: offset = GPIO_EXT_PORTA_OFFSET; break;
        case 1: offset = GPIO_EXT_PORTB_OFFSET; break;
        case 2: offset = GPIO_EXT_PORTC_OFFSET; break;
        case 3: offset = GPIO_EXT_PORTD_OFFSET; break;
        default: return 0; /* Invalid port */
    }
    return *(base + (offset / sizeof(uint32_t)));
}

void gpio_ext_port_write(uint8_t port, uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    uint32_t offset;
    switch (port) {
        case 0: offset = GPIO_EXT_PORTA_OFFSET; break;
        case 1: offset = GPIO_EXT_PORTB_OFFSET; break;
        case 2: offset = GPIO_EXT_PORTC_OFFSET; break;
        case 3: offset = GPIO_EXT_PORTD_OFFSET; break;
        default: return; /* Invalid port */
    }
    *(base + (offset / sizeof(uint32_t))) = value;
}

/* Synchronization level register */
uint32_t gpio_ls_sync_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_LS_SYNC_OFFSET / sizeof(uint32_t)));
}

void gpio_ls_sync_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_LS_SYNC_OFFSET / sizeof(uint32_t))) = value;
}

/* GPIO ID code register */
uint32_t gpio_id_code_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_ID_CODE_OFFSET / sizeof(uint32_t)));
}

void gpio_id_code_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_ID_CODE_OFFSET / sizeof(uint32_t))) = value;
}

/* Interrupt both edge type register */
uint32_t gpio_int_bothedge_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_INT_BOTHEDGE_OFFSET / sizeof(uint32_t)));
}

void gpio_int_bothedge_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_INT_BOTHEDGE_OFFSET / sizeof(uint32_t))) = value;
}

/* GPIO component version register */
uint32_t gpio_ver_id_code_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_VER_ID_CODE_OFFSET / sizeof(uint32_t)));
}

void gpio_ver_id_code_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_VER_ID_CODE_OFFSET / sizeof(uint32_t))) = value;
}

/* GPIO Configuration Register 2 */
uint32_t gpio_config_reg2_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_CONFIG_REG2_OFFSET / sizeof(uint32_t)));
}

void gpio_config_reg2_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_CONFIG_REG2_OFFSET / sizeof(uint32_t))) = value;
}

/* GPIO Configuration Register 1 */
uint32_t gpio_config_reg1_read(void) {
    volatile uint32_t* base = gpio_get_base();
    return *(base + (GPIO_CONFIG_REG1_OFFSET / sizeof(uint32_t)));
}

void gpio_config_reg1_write(uint32_t value) {
    volatile uint32_t* base = gpio_get_base();
    *(base + (GPIO_CONFIG_REG1_OFFSET / sizeof(uint32_t))) = value;
}