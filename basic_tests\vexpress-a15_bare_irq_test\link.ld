/* 链接脚本link.ld */
MEMORY {
    RAM (rwx) : ORIGIN = 0x80000000, LENGTH = 512K
}

SECTIONS {
    /* 异常向量表 */
    .vectors :
    {
        . = ORIGIN(RAM);
        . = ALIGN(32);
        __exception_vector_start = .;
        KEEP(*(.vectors))
        . = ALIGN(32);
    } > RAM

    .text : {
        *(.text)
    } > RAM

    .data : {
        *(.data)
    } > RAM

    .bss : {
        *(.bss)
    } > RAM

    . = ALIGN(8);
    stack_top = . + 0x1000;
}