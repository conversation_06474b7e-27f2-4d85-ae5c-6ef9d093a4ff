#include "watchdog.h"
/*
 ****************************************************************************
 *
 * 名称：watchdog_test
 *
 * 功能：看门狗功能测试主函数
 *
 * 说明：依次执行各项看门狗测试用例
 *
 * *****************************************************************************
*/
void watchdog_test(void)
{
    /* 寄存器测试，含有复位/循环操作，测试下述功能测试时将其注释掉！ */
    register_test();

    /* 功能测试 */
    test_mode_switch();       /* 模式切换测试 */
    test_feed_watchdog();     /* 喂狗测试 */
    test_not_feed_watchdog(); /* 非喂狗测试 */
    test_watchdog_timeout();  /* 看门狗超时测试 */
}

/*
 ****************************************************************************
 *
 * 名称：test_mode_switch
 *
 * 功能：模式切换测试
 *
 * 说明：测试定时器模式与看门狗模式之间的切换
 *
 * *****************************************************************************
*/
void test_mode_switch(void)
{
    /* 初始化为定时器模式 */
    Bsp_CpuWdtDisable();      /* 禁用看门狗模式(切换到定时器模式) */
    Bsp_CpuWdtStop();         /* 停止计数器 */
    
    /* 切换到看门狗模式 */
    Bsp_CpuWdtModeSet();      /* 设置为看门狗模式 */
    Bsp_CpuWdtEnable();       /* 启用看门狗 */
    
    /* 切回定时器模式 */
    Bsp_CpuWdtDisable();      /* 禁用看门狗模式(切换到定时器模式) */
    Bsp_CpuWdtStop();         /* 停止计数器 */
}

/*
 ****************************************************************************
 *
 * 名称：test_watchdog_timeout
 *
 * 功能：看门狗超时测试
 *
 * 说明：启用看门狗后不喂狗，验证系统是否复位
 *
 * *****************************************************************************
*/
void test_watchdog_timeout(void)
{
    /* 启用看门狗模式，设置短超时时间 */
    Bsp_CpuWdtModeSet();
    Bsp_CpuWdtEnable();
    
    /* 循环等待超时（不喂狗） */
    while (1) {
        /* 等待超时触发复位 */
    }
}

/*
 ****************************************************************************
 *
 * 名称：test_feed_watchdog
 *
 * 功能：喂狗测试
 *
 * 说明：定期喂狗，验证系统是否保持运行
 *
 * *****************************************************************************
*/
void test_feed_watchdog(void)
{
    U32 loop_count = 0;
    
    /* 启用看门狗模式*/
    Bsp_CpuWdtModeSet();
    Bsp_CpuWdtEnable();
    
    // 记录启动时间（使用WDT_COUNTER作为时间参考）
    uint32_t start_value = READ_32(WDT_COUNTER);

    /* 循环喂狗指定次数 */
    while (loop_count < WDT_TEST_LOOP_COUNT) {
        /* 喂狗：重新加载计数器值 */
        Bsp_CpuWdtEnable();
        
        /* 等待一段时间，但不超过看门狗超时时间 */
        while ((start_value - READ_32(WDT_COUNTER)) < 500);
        start_value = READ_32(WDT_COUNTER);
        
        loop_count++;
    }
    
    /* 测试完成，关闭看门狗 */
    Bsp_CpuWdtStop();
}

/*
 ****************************************************************************
 *
 * 名称：test_not_feed_watchdog
 *
 * 功能：非喂狗测试
 *
 * 说明：启用看门狗后不喂狗，验证系统是否复位
 *
 * *****************************************************************************
*/
void test_not_feed_watchdog(void)
{
    /* 启用看门狗，设置中等超时时间 */
    Bsp_CpuWdtModeSet();
    Bsp_CpuWdtEnable();
    
    /* 等待时间超过看门狗超时时间，期间不喂狗 */
    while (1);
    
    /* 如果代码执行到这里，说明看门狗未触发复位，测试失败 */
    while (1); /* 陷入死循环 */
}

/*
 ****************************************************************************
 *
 * 名称：register_test
 *
 * 功能：看门狗寄存器功能测试
 *
 * 说明：测试看门狗各种寄存器操作和功能
 *
 * *****************************************************************************
*/
void register_test(void)
{
    /* 检查是否因看门狗复位而启动 */
    if (Bsp_CpuWdtGetResetStatus()) {
        /* 承接测试3内容：验证中断状态未被触发（忽略IRQ_EN位的效果） */
        if ((READ_32(BSP_AMBA_WDT_BASEADDR + BSP_WDT_INT_STATUS_OFFSET_REG) & 0x1) != 0) {
            while(1); /* 测试失败：中断被错误触发 */
        }

        /* 测试4：清除复位标志位测试（此时已经进入看门狗复位判断逻辑） */     
        Bsp_CpuWdtClearResetStatus();
        
        if (Bsp_CpuWdtGetResetStatus() != 0) {
            while(1); /* 复位标志位未清除，测试失败 */
        }
        
        /* 测试5：看门狗模式切换 */
        Bsp_CpuWdtDisable();
        
        if ((READ_32(BSP_AMBA_WDT_BASEADDR + BSP_WDT_CONTROL_OFFSET_REG) & 0x8) != 0) {
            while(1); /* 测试失败：看门狗模式未关闭 */
        }

    } else {
        /* 测试1：尝试直接设置WDT_CONTR的[3]位为1，开启看门狗模式 */
        WRITE_32(BSP_AMBA_WDT_BASEADDR + BSP_WDT_CONTROL_OFFSET_REG, 
                READ_32(BSP_AMBA_WDT_BASEADDR + BSP_WDT_CONTROL_OFFSET_REG) | 0x8);
        
        if ((READ_32(BSP_AMBA_WDT_BASEADDR + BSP_WDT_CONTROL_OFFSET_REG) & 0x8) != 0x8) {
            while(1); /* 若是进入循环则证明测试失败，未成功开启看门狗模式 */
        }

        /* 测试2：在看门狗模式下，无法通过写入WDT_CONTR的[3]位为0切换至定时器模式 */
        Bsp_CpuWdtModeSet(); /* 为了确保依旧处于看门狗状态，通过正确方式设置看门狗模式 */
        
        WRITE_32(BSP_AMBA_WDT_BASEADDR + BSP_WDT_CONTROL_OFFSET_REG, 
                READ_32(BSP_AMBA_WDT_BASEADDR + BSP_WDT_CONTROL_OFFSET_REG) & ~0x8); /* 尝试清除看门狗模式位、进入定时器模式 */
        
        if ((READ_32(BSP_AMBA_WDT_BASEADDR + BSP_WDT_CONTROL_OFFSET_REG) & 0x8) == 0) {
            while(1); /* 若是进入循环则证明测试失败，寄存器[3]位被错误清除 */
        }

        /* 测试3：在看门狗模式下，忽略WDT_CONTR的中断使能位 */
        Bsp_CpuWdtModeSet(); /* 确保处于看门狗模式 */
        WRITE_32(BSP_AMBA_WDT_BASEADDR + BSP_WDT_CONTROL_OFFSET_REG, 
                READ_32(BSP_AMBA_WDT_BASEADDR + BSP_WDT_CONTROL_OFFSET_REG) | 0x4); /* 设置中断使能位 */

        /* 验证硬件已接受IRQ_EN位的写入 */
        if ((READ_32(BSP_AMBA_WDT_BASEADDR + BSP_WDT_CONTROL_OFFSET_REG) & 0x4) == 0) {
            while(1); /* 测试失败：硬件拒绝写入IRQ_EN位 */
        }

        /* 准备触发看门狗超时 */
        Bsp_CpuWdtEnable();

        /* 等待超时 */
        while(1);
        
    }
}
