/* main.c - 定时器中断配置 */
#include <stdint.h>

// GIC寄存器
#define GICD_BASE 0x1E001000
#define GICC_BASE 0x1E000100

/* GIC Distributor 寄存器*/
#define GICD_ICCDCR         (*(volatile uint32_t*)(GICD_BASE + 0x000))  /* 分配器控制寄存器 */
#define GICD_ICDICTR        (*(volatile uint32_t*)(GICD_BASE + 0x004))  /* 中断控制器类型寄存器 - RO GIv2 */
#define GICD_ICDIIDR        (*(volatile uint32_t*)(GICD_BASE + 0x008))  /* 分配器版本信息寄存器 - RO 0x0000043B */
#define GICD_ICDISRn        (*(volatile uint32_t*)(GICD_BASE + 0x080))  /* 中断安全（分组）寄存器 */
#define GICD_ICDISERn       (*(volatile uint32_t*)(GICD_BASE + 0x100))  /* 中断使能寄存器 */
#define GICD_ICDICERn       (*(volatile uint32_t*)(GICD_BASE + 0x180))  /* 中断挂起清除寄存器组 */
#define GICD_ICDISPRn       (*(volatile uint32_t*)(GICD_BASE + 0x200))  /* 中断挂起设置寄存器组 */
#define GICD_ICDICPRn       (*(volatile uint32_t*)(GICD_BASE + 0x280))  /* 中断挂起清除寄存器组 */
#define GICD_ICDIABRn       (*(volatile uint32_t*)(GICD_BASE + 0x300))  /* 中断 Active 寄存器组 - RO */
#define GICD_ICDIPRn        (*(volatile uint32_t*)(GICD_BASE + 0x400))  /* 中断优先级寄存器组 */
#define GICD_ICDIPTRn       (*(volatile uint32_t*)(GICD_BASE + 0x800))  /* 中断目标 CPU 寄存器组 - 单核 RAZ/WI */
#define GICD_ICDICFRn       (*(volatile uint32_t*)(GICD_BASE + 0xC00))  /* 中断配置寄存器组 */
#define GICD_ICDSGIR        (*(volatile uint32_t*)(GICD_BASE + 0xF00))  /* SGI 触发寄存器 */

/* GIC CPU Interface 寄存器*/
#define GICC_ICCICR         (*(volatile uint32_t*)(GICC_BASE + 0x00))   /* CPU 接口控制寄存器 */
#define GICC_ICCPMR         (*(volatile uint32_t*)(GICC_BASE + 0x04))   /* 中断优先级屏蔽寄存器 */
#define GICC_ICCBPR         (*(volatile uint32_t*)(GICC_BASE + 0x08))   /* 二进制点寄存器 (Secure) */
#define GICC_ICCIAR         (*(volatile uint32_t*)(GICC_BASE + 0x0C))   /* 中断响应寄存器 - RO */
#define GICC_ICCEOIR        (*(volatile uint32_t*)(GICC_BASE + 0x10))   /* 中断结束寄存器 - WO */
#define GICC_ICCRPR         (*(volatile uint32_t*)(GICC_BASE + 0x14))   /* 响应中断优先级寄存器 - RO */
#define GICC_ICCHPIR        (*(volatile uint32_t*)(GICC_BASE + 0x18))   /* 最高挂起中断寄存器 - RO */
#define GICC_ICCABPR        (*(volatile uint32_t*)(GICC_BASE + 0x1C))   /* 二进制点寄存器 (Non-Secure) */

// 定时器寄存器（SP804）
#define TIMER_IRQ_ID 80
#define TIMER_BASE 0x100E4000
#define TIMER_LOAD  (*(volatile uint32_t*)(TIMER_BASE + 0x00))
#define TIMER_VALUE (*(volatile uint32_t*)(TIMER_BASE + 0x04))
#define TIMER_CTRL  (*(volatile uint32_t*)(TIMER_BASE + 0x08))
#define TIMER_INTCLR (*(volatile uint32_t*)(TIMER_BASE + 0x0C))

volatile uint32_t irq_count = 0;       // 中断计数器

void gic_handle_irq(void) {
    uint32_t iar = GICC_ICCIAR;        // 读取中断ID
    uint32_t irq_id = iar & 0x3FF;     // ICCIAR [9:0]

    if (irq_id == TIMER_IRQ_ID) {                 // 定时器中断号为 34
        irq_count++;                   // 更新计数器
        TIMER_INTCLR = 1;              // 清除定时器中断标志
    }

    GICC_ICCEOIR = iar;                // 通知GIC中断处理完成
}

int get_ns_bit(void) {
    uint32_t scr;
    // 通过 CP15 协处理器读取 SCR 寄存器
    __asm__ volatile("mrc p15, 0, %0, c1, c1, 0" : "=r"(scr));
    return (scr & 1); // 直接返回 NS 位（第 0 位）
}

int main() {
    int ns_flag = 100;
    ns_flag = get_ns_bit();
    int count = 0;

    // 初始化 GIC
    GICD_ICCDCR = 0b11;                // 使能GIC分配器
    // GICD_ICDISERn = 0xFFFFFFFF;        
    // (*(volatile uint32_t*)(GICD_BASE + 0x104)) = 0xFFFFFFFF; // 使能中断 [63:32]
    (*(volatile uint32_t*)(GICD_BASE + 0x108)) = 0x10000; // 使能中断 [80]
    GICC_ICCPMR = 0xFF;               // 设置优先级阈值
    GICC_ICCICR = 0b10111;            // 使能CPU接口

    // enable_irq();                     // 全局使能IRQ

    // 设置 SP804
    TIMER_LOAD = 1000;            // 加载计数值为 1000 （1ms 触发一次中断） sp804的频率为 1MHz 即 1 us 计数一次
    TIMER_CTRL = (1 << 7) | (1 << 6) | (1 << 5) | (0 << 2); // 定时器使能、周期模式、中断使能、分频系数 1

    while (irq_count < 5) {
        // 等待5次中断触发
    }            

    return 0;
}