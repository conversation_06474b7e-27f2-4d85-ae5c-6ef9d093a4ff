#include <stdint.h>
#include <stdbool.h>

// 假设IOMUX外设的基地址（需根据具体硬件手册调整）
#define IOMUX_BASE (0x4000F000)

// 每个PAD的寄存器偏移量计算
static inline volatile uint32_t* iomux_pad_reg(uint8_t pad_num) {
    // 确保pad_num < 64，此处可添加断言
    return (volatile uint32_t*)(IOMUX_BASE + pad_num * 4);
}

// 寄存器位域掩码定义
#define ST_MASK  (1U << 0)
#define DS_MASK  (0xFU << 1)
#define PD_MASK  (1U << 5)
#define PU_MASK  (1U << 6)
#define OE_MASK  (1U << 7)
#define IE_MASK  (1U << 8)
#define SL_MASK  (1U << 10)
#define SEL_MASK (0x7U << 11)
#define DI_MASK  (1U << 31)

/* ================ 寄存器配置函数 ================ */

// Schmitt Trigger使能设置
static inline void iomux_set_st(volatile uint32_t *reg, bool enable) {
    *reg = (*reg & ~ST_MASK) | (enable ? ST_MASK : 0);
}

// 驱动强度设置 (0-15)
static inline void iomux_set_ds(volatile uint32_t *reg, uint8_t ds) {
    *reg = (*reg & ~DS_MASK) | ((ds & 0xFU) << 1);
}

// 下拉电阻使能
static inline void iomux_set_pd(volatile uint32_t *reg, bool enable) {
    *reg = (*reg & ~PD_MASK) | (enable ? PD_MASK : 0);
}

// 上拉电阻使能
static inline void iomux_set_pu(volatile uint32_t *reg, bool enable) {
    *reg = (*reg & ~PU_MASK) | (enable ? PU_MASK : 0);
}

// 输出使能
static inline void iomux_set_oe(volatile uint32_t *reg, bool enable) {
    *reg = (*reg & ~OE_MASK) | (enable ? OE_MASK : 0);
}

// 输入使能
static inline void iomux_set_ie(volatile uint32_t *reg, bool enable) {
    *reg = (*reg & ~IE_MASK) | (enable ? IE_MASK : 0);
}

// 压摆率控制
static inline void iomux_set_sl(volatile uint32_t *reg, bool enable) {
    *reg = (*reg & ~SL_MASK) | (enable ? SL_MASK : 0);
}

// 功能选择设置 (func_no: 1-8)
static inline void iomux_set_sel(volatile uint32_t *reg, uint8_t func_no) {
    uint8_t val = func_no - 1;  // 转换为0-7
    *reg = (*reg & ~SEL_MASK) | ((val & 0x7U) << 11);
}

/* ================ 寄存器读取函数 ================ */

// 获取Schmitt Trigger状态
static inline bool iomux_get_st(volatile uint32_t *reg) {
    return (*reg & ST_MASK) != 0;
}

// 获取驱动强度
static inline uint8_t iomux_get_ds(volatile uint32_t *reg) {
    return (uint8_t)((*reg & DS_MASK) >> 1);
}

// 获取下拉状态
static inline bool iomux_get_pd(volatile uint32_t *reg) {
    return (*reg & PD_MASK) != 0;
}

// 获取上拉状态
static inline bool iomux_get_pu(volatile uint32_t *reg) {
    return (*reg & PU_MASK) != 0;
}

// 获取输出使能状态
static inline bool iomux_get_oe(volatile uint32_t *reg) {
    return (*reg & OE_MASK) != 0;
}

// 获取输入使能状态
static inline bool iomux_get_ie(volatile uint32_t *reg) {
    return (*reg & IE_MASK) != 0;
}

// 获取压摆率配置
static inline bool iomux_get_sl(volatile uint32_t *reg) {
    return (*reg & SL_MASK) != 0;
}

// 获取当前功能号 (返回1-8)
static inline uint8_t iomux_get_sel(volatile uint32_t *reg) {
    return (uint8_t)(((*reg & SEL_MASK) >> 11) + 1);
}

// 获取输入数据状态
static inline bool iomux_get_di(volatile uint32_t *reg) {
    return (*reg & DI_MASK) != 0;
}

/* ================ 高级封装函数 ================ */

// 完全配置一个PAD的参数
void iomux_configure_pad(
    uint8_t pad_num,
    bool st,        // Schmitt Trigger
    uint8_t ds,     // Drive Strength (0-15)
    bool pd,        // Pull Down
    bool pu,        // Pull Up
    bool oe,        // Output Enable
    bool ie,        // Input Enable
    bool sl,        // Slew Rate
    uint8_t func_no // Function Select (1-8)
) {
    volatile uint32_t *reg = iomux_pad_reg(pad_num);
    
    *reg = 0; // 先清零寄存器（根据需求可选）
    
    iomux_set_st(reg, st);
    iomux_set_ds(reg, ds);
    iomux_set_pd(reg, pd);
    iomux_set_pu(reg, pu);
    iomux_set_oe(reg, oe);
    iomux_set_ie(reg, ie);
    iomux_set_sl(reg, sl);
    iomux_set_sel(reg, func_no);
}

/* 使用示例：
int main() {
    // 配置PAD 0:
    // - 启用施密特触发
    // - 驱动强度8
    // - 启用上拉
    // - 输出使能
    // - 选择功能3
    iomux_configure_pad(0, true, 8, false, true, true, false, false, 3);
    
    // 单独修改PAD 1的驱动强度
    volatile uint32_t *pad1_reg = iomux_pad_reg(1);
    iomux_set_ds(pad1_reg, 12);
    
    // 读取PAD 2的输入状态
    volatile uint32_t *pad2_reg = iomux_pad_reg(2);
    bool input_state = iomux_get_di(pad2_reg);
    
    return 0;
}
*/