/*
 * 适用于 soc2018 的 SDRAM 内存、控制器及 EDAC 功能测试程序
 */

#include "types.h"
#include "gic.h"        /* 中断控制功能 */
#include "bsp_print.h"  /* 串口输出功能 */
#include "sdram.h"  // 引入外设驱动头文件

#define SDRAM_SIZE 0x10000000

extern int irq_count;

void memory_access_test(void) {
    uint32_t val = 0;
    volatile unsigned int *ptr = (volatile unsigned int *)0x40000000;

    *ptr = 0xFFFFFFFF;
    val = *ptr; // 应为 0d4294967295

    ptr = (volatile unsigned int *)0xC0000000;
    *ptr = 0xFFFFFFFF;
    val = *ptr; // 应为 0d4294967295

    ptr = (volatile unsigned int *)0xCA000000;
    *ptr = 0xFFFFFFFF;
    val = *ptr; // 应为 0d4294967295
}

void sdram_ctrl_register_test(void) {
    uint32_t val = 0;

    val = READ_32(SDRAM_SCONR);
    // val == 0x1C3388

    val = READ_32(SDRAM_STMG0R);
    // val == 0x2261496

    val = READ_32(SDRAM_STMG1R);
    // val == 0x70008

    val = READ_32(SDRAM_SCTLR); 
    // val == 0x00003008 0d12296

    val = READ_32(SDRAM_SREFR);
    // val == 0x410

    val = READ_32(SDRAM_SCSLR0_LOW);
    // val == 0x80000000

    val = READ_32(SDRAM_SMSKR0);
    // val 依据 SDRAM 大小

    val = READ_32(SDRAM_EDAC_CTL);
    // val == 0x8
    
    val = READ_32(SDRAM_ONEBIT_ERR_ADDR);
    // val == 0xB00000C4

    val = READ_32(SDRAM_ONEBIT_ERR_DATA);
    // val == 0

    val = READ_32(SDRAM_ONEBIT_ERR_EDAC);
    // val == 0

    val = READ_32(SDRAM_TWOBITS_ERR_ADDR);
    // val == 0xB00000D0

    val = READ_32(SDRAM_TWOBITS_ERR_DATA);
    // val == 0

    val = READ_32(SDRAM_TWOBITS_ERR_EDAC);
    // val == 0

    val = READ_32(SDRAM_RD_EDAC_ADDR_BYPASS);
    // val == 0xB00000DC

    val = READ_32(SDRAM_RD_EDAC_BYPASS);
    // val == 0

    val = READ_32(SDRAM_RD_DATA_BYPASS);
    // val == 0

    val = READ_32(SDRAM_WR_EDAC_ADDR_BYPASS);
    // val == 0xB00000E8

    val = READ_32(SDRAM_WR_EDAC_DATA_BYPASS);
    // val == 0

    val = READ_32(SDRAM_COMP_TYPE);
    // val == 0x44572110

    val = READ_32(SDRAM_COMP_VERSION);
    // val == 0x3237392A

    val = READ_32(SDRAM_COMP_PARAMS_1);
    // val == 0

    val = READ_32(SDRAM_COMP_PARAMS_2);
    // val == 0x00004C4B
}

void sdram_write_read_bypass_test(void) {
    /**
     * EDAC 写旁路测试
     */
    /* 先将待写旁路测试的地址空间附近写上数据 */
    volatile unsigned int *ptr = (volatile unsigned int *)0xC0000000;
    *ptr++ = 0xFFFFFFFF;
    *ptr++ = 0x9A9A9A9A; /* 0xC0000004 */
    *ptr = 0xFFFFFFFF;

    /*
     * 使能写旁路  
     */ 
    uint32_t cfg = 0xC0000004;
    WRITE_32(SDRAM_WR_EDAC_ADDR_BYPASS, cfg);           /* 设置写旁路地址 */
    WRITE_32(SDRAM_WR_EDAC_DATA_BYPASS, 0x1F1F1F1F);    /* 设置注入校验码 */
    WRITE_32(SDRAM_EDAC_CTL, 0b1101);                   /* 使能写旁路 */

    ptr = (volatile unsigned int *)0xC0000004;
    *ptr = 0;

    // 此时 0xC0000004 数据空间为 0，对应的校验空间的 20bit 为全 1，之后及之前 20bit 为 4 组 0b01100

    /* 配置读旁路并使能，以查看校验数据是否为写旁路注入的 */
    WRITE_32(SDRAM_RD_EDAC_ADDR_BYPASS, cfg);
    WRITE_32(SDRAM_EDAC_CTL, 0b1111);  /* 使能读旁路 */

    uint32_t val = *ptr; /* 触发读旁路 */
    /* 读 EDAC 读旁路数据/校验码寄存器 */
    uint32_t data = READ_32(SDRAM_RD_DATA_BYPASS);

    // data == val == 0

    uint32_t edac = READ_32(SDRAM_RD_EDAC_BYPASS);
    
    // edac == 0x1F1F1F1F

    cfg = 0xC0000008;
    ptr = (volatile unsigned int *)0xC0000008;
    WRITE_32(SDRAM_WR_EDAC_ADDR_BYPASS, cfg);           /* 设置写旁路地址 */
    *ptr = 0;   /* 触发写旁路 */

    // 此时 0xC0000008 对应的校验空间的 20bit 为全 1

    /* 通过读旁路读前后的校验码 */
    cfg = 0xC0000000;
    ptr = (volatile unsigned int *)0xC0000000;
    WRITE_32(SDRAM_RD_EDAC_ADDR_BYPASS, cfg);
    val = *ptr; /* 触发读旁路 */
    edac = READ_32(SDRAM_RD_EDAC_BYPASS);

    // edac == 0x18181818

    cfg = 0xC0000008;
    ptr = (volatile unsigned int *)0xC0000008;
    WRITE_32(SDRAM_RD_EDAC_ADDR_BYPASS, cfg);
    val = *ptr; /* 触发读旁路 */
    edac = READ_32(SDRAM_RD_EDAC_BYPASS);

    // edac == 0x1F1F1F1F

    WRITE_32(SDRAM_EDAC_CTL, 0b1011);   /* 禁用写旁路 */
    *ptr = 0;                           /* 使其校验码全 0 */
    val = *ptr;
    edac = READ_32(SDRAM_RD_EDAC_BYPASS);

    // edac == 0x00000000

    /* 读旁路访问 0xC0000004 不会报错 */
    cfg = 0xC0000004;
    ptr = (volatile unsigned int *)0xC0000004;
    WRITE_32(SDRAM_RD_EDAC_ADDR_BYPASS, cfg);
    val = *ptr; /* 不报错 */

    /* 禁用读旁路，访问 0xC0000004 会产生错误 */
    ptr = (volatile unsigned int *)0xC0000004;
    WRITE_32(SDRAM_EDAC_CTL, 0b1001);   /* 禁用读旁路 */
    val = *ptr; /* EDAC 检出错误，报错 */
}

void sdram_interrupt_test(void) {
    
}

int main(void)
{
    /* GIC 初始化 */
    gic_init();
    
    /* 业务代码开始 */
    // memory_access_test();
    // sdram_ctrl_register_test();
    sdram_write_read_bypass_test();
    /* 业务代码结束 */

    return 0;
}
