/*
 * SRAM 驱动头文件
 */

#ifndef SRAM_H
#define SRAM_H

/* SRAM 寄存器定义 */
#define SRAM_BASE                           0x40200000
#define SRAM_MEM_CONFIG                     (SRAM_BASE + 0x00)    /* RW 空间配置寄存器 使用 [3:0] 位域 */
#define SRAM_EDAC_BYPASS_DATAIN             (SRAM_BASE + 0x08)    /* RW EDAC 旁路数据注入寄存器 使用每字节低 5 bit */
#define SRAM_ADDR_ONE_BIT_REG               (SRAM_BASE + 0x10)    /* RO 最近单错地址寄存器 */
#define SRAM_ADDR_ONE_BIT_COUNT             (SRAM_BASE + 0x18)    /* RW 单错计数寄存器 */
#define SRAM_ADDR_TWO_BIT_REG               (SRAM_BASE + 0x20)    /* RO 最近双错地址寄存器 */
#define SRAM_EDAC_READBYPASS_DATA_LOW       (SRAM_BASE + 0x28)    /* RO EDAC 读旁路数据低位寄存器 */
#define SRAM_EDAC_READBYPASS_DATA_HIGH      (SRAM_BASE + 0x2C)    /* RO EDAC 读旁路数据高位寄存器 */
#define SRAM_ONE_BIT_CLEAR                  (SRAM_BASE + 0x30)    /* WO 清单错中断寄存器 */
#define SRAM_TWO_BIT_CLEAR                  (SRAM_BASE + 0x38)    /* WO 清多错中断寄存器 */
#define SRAM_BYTE_ERR_REG                   (SRAM_BASE + 0x40)    /* RO 单双错字节标识寄存器 */
#define SRAM_EDAC_READ_BYPASS_ADDR          (SRAM_BASE + 0x48)    /* WO EDAC 读旁路配置地址寄存器 */
#define SRAM_EDAC_WRITE_BYPASS_ADDR         (SRAM_BASE + 0x50)    /* WO EDAC 写旁路配置地址寄存器 */
#define SRAM_DATA_TWO_BIT_REG               (SRAM_BASE + 0x58)    /* RO 最近双错数据寄存器 */
#define SRAM_EDAC_TWO_BIT_REG               (SRAM_BASE + 0x60)    /* RO 最近双错校验码寄存器 */

#endif /* SRAM_H */
