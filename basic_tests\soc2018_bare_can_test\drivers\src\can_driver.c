#include "gic.h"
#include "types.h"
#include "can_sja1000.h"
#include "bsp_print.h"

void RI_isr(uint64_t can_base){//后续根据502驱动修改
    print2("[RI_isr]=====Receive Interrupt=====\n");
    //main使能了接收中断
    //处理阶段：读取16~28地址，其中rx_buff是按照rxbuf_start循环的
    READ_8(can_base+0x10);//16
    READ_8(can_base+0x11);//17
    READ_8(can_base+0x12);//18
    READ_8(can_base+0x13);//19
    READ_8(can_base+0x14);//20
    READ_8(can_base+0x15);//21
    READ_8(can_base+0x16);//0x16=0001 0110,2+4+16=22
    READ_8(can_base+0x17);//23
    READ_8(can_base+0x18);//24
    READ_8(can_base+0x19);//25
    READ_8(can_base+0x1A);//26
    READ_8(can_base+0x1B);//27
    READ_8(can_base+0x1C);//28

    //清除阶段：清除接收缓冲区
    //触发CMR寄存器以清除接收缓冲区：写第2位为1
    WRITE_8(can_base+SJA1000_CMR,0x04);//清除接收缓冲区，此时若rxmsg_cnt == 0，会中断下拉
}

void TI_isr(uint64_t can_base){
    //业务逻辑：判断是否还有数据需要发送，这里用打印代替业务
    print2("[TI_isr]=====No Data Need To Transfer=====\n");

    //清除发送中断由can_isr的读取状态寄存器动作内部自动完成
}

void DOI_isr(uint64_t can_base){
    //业务逻辑：具体操作将取决于系统设计（如扩充内存缓冲区），这里用打印代替业务
    print2("[DOI_isr]=====DATA Overrun, Please Check Your Memory=====\n");

    //清除数据溢出中断由can_isr的读取状态寄存器动作内部自动完成
}

void WUI_isr(uint64_t can_base){
    //业务逻辑：具体操作将取决于系统设计（如恢复发送），这里用打印代替业务
    //读取模式寄存器
    uint8_t mode = READ_8(can_base+SJA1000_MOD);
    print2("[WUI_isr]=====Wake Up Interrupt=====\n"); 

    //清除唤醒中断由can_isr的读取状态寄存器动作内部自动完成
}

void EI_isr(uint64_t can_base){
   //业务逻辑：具体操作将取决于系统设计，这里用打印代替业务
   print2("[EI_isr]=====Error Interrupt=====\n");
   //清除错误中断由can_isr的读取状态寄存器动作内部自动完成 
}

void EPI_isr(uint64_t can_base){
   //业务逻辑：具体操作将取决于系统设计，这里用打印代替业务
   print2("[EPI_isr]=====Error Passive Interrupt=====\n");
   //清除错误中断由can_isr的读取状态寄存器动作内部自动完成 
}

void can1_isr(){
    print2("[can1_isr]\n"); 
    //判断阶段：读取CAN1中断状态寄存器，判断是哪种中断，同时内部会清零IR，若还有数据未处理则仅触发接收中断【拉高】【会陷入死循环吗】
    uint8_t irq_status = READ_8(CAN1_BASE_ADDR+SJA1000_IR);//读取中断状态寄存器
    print2("irq1_status = 0x%x\n",irq_status);
    uint8_t en = READ_8(CAN1_BASE_ADDR+SJA1000_IER);
    uint8_t mode = READ_8(CAN1_BASE_ADDR+SJA1000_MOD);
    uint8_t status = READ_8(CAN1_BASE_ADDR+SJA1000_SR);
    print2("can1_status = 0x%x\n",status);
    //处理阶段：根据中断类型，处理中断
    if((irq_status & 0x01) && (en & 0x01)){//接收中断
        RI_isr(CAN1_BASE_ADDR);
    }
    if((irq_status & 0x02) && (en & 0x02)){//发送中断
        TI_isr(CAN1_BASE_ADDR);
    }
    if((irq_status & 0x08) && (en & 0x08)){//数据溢出中断
        DOI_isr(CAN1_BASE_ADDR);
    }
    if((irq_status & 0x10) && (en & 0x10)){//唤醒中断
        WUI_isr(CAN1_BASE_ADDR);
    }
    if((irq_status & 0x04) && (en & 0x04)){//错误警告中断
        EI_isr(CAN1_BASE_ADDR);
                //如果是复位模式，则设置为操作模式
        if(mode & 0x01 == 0x01){
            print2("[can1_isr]=====Reset Mode to Operation Mode=====\n");
            WRITE_8(CAN1_BASE_ADDR+SJA1000_MOD,0x08);//单过滤操作模式
        }
    }
    if((irq_status & 0x20) && (en & 0x20)){//错误被动中断
        EPI_isr(CAN1_BASE_ADDR);
    }
}

void can0_isr(){
    print2("[can0_isr]\n"); 
    //判断阶段：读取CAN1中断状态寄存器，判断是哪种中断，同时内部会清零IR，若还有数据未处理则仅触发接收中断【拉高】【会陷入死循环吗】
    uint8_t irq_status = READ_8(CAN0_BASE_ADDR+SJA1000_IR);//读取中断状态寄存器
    print2("irq0_status = 0x%x\n",irq_status);
    uint8_t en = READ_8(CAN0_BASE_ADDR+SJA1000_IER);
    uint8_t mode = READ_8(CAN0_BASE_ADDR+SJA1000_MOD);
    uint8_t status = READ_8(CAN0_BASE_ADDR+SJA1000_SR);
    print2("can0_status = 0x%x\n",status);
    //处理阶段：根据中断类型，处理中断
    if((irq_status & 0x01) && (en & 0x01)){//接收中断
        RI_isr(CAN0_BASE_ADDR);
    }
    if((irq_status & 0x02) && (en & 0x02)){//发送中断
        TI_isr(CAN0_BASE_ADDR);
    }
    if((irq_status & 0x08) && (en & 0x08)){//数据溢出中断
        DOI_isr(CAN0_BASE_ADDR);
    }
    if((irq_status & 0x10) && (en & 0x10)){//唤醒中断
        WUI_isr(CAN0_BASE_ADDR);
    }
    if((irq_status & 0x04) && (en & 0x04)){//错误警告中断
        EI_isr(CAN0_BASE_ADDR);
        //如果是复位模式，则设置为操作模式
        if(mode & 0x01 == 0x01){
            print2("[can0_isr]=====Reset Mode to Operation Mode=====\n");
            WRITE_8(CAN0_BASE_ADDR+SJA1000_MOD,0x08);//单过滤操作模式
        }
    }
    if((irq_status & 0x20) && (en & 0x20)){//错误被动中断
        EPI_isr(CAN0_BASE_ADDR);
    }
}

