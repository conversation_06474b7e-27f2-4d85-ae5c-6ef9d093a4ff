/*
 * 适用于soc2018 A9MPCore 的看门狗测试程序
 */

#include "types.h"
#include "gic.h"        /* 中断控制功能 */
#include "bsp_print.h"  /* 串口输出功能 */
#include "watchdog.h"  // 引入外设驱动头文件

extern int irq_count;

int main(void)
{
    /* GIC 初始化 */
    gic_init();
    
    /* 业务代码开始 */
    watchdog_test();
    
    // WRITE_32(WDT_LOAD, 50000); // 较小的加载值，更快超时
    // WRITE_32(WDT_CTRL, 1<<0 | 1<<3);
    // WRITE_32(WDT_CTRL, 1<<3);
    // 等待时间超过看门狗超时时间，期间不喂狗
    while (1);
    /* 业务代码结束 */

    return 0;
}
