
#ifndef TYPES_H
#define TYPES_H

#include "soc502_parameter.h"

typedef unsigned int         u32;
typedef unsigned short       u16;
typedef unsigned char        u8;
typedef unsigned long long   u64;

typedef int                  s32;
typedef short                s16;
typedef char                 s8;
typedef char *               charptr;
typedef int *                intptr;

//#define DEBUG

#ifndef NULL
  #define NULL       0
#endif

#ifndef FALSE 
	#define FALSE      0
#endif

#ifndef TRUE
	#define TRUE       1
#endif

#define r32(addr)           (*(volatile u32 *)(addr))
#define w32(addr, val)      (*(volatile u32 *)(addr) = (val))
#define r16(addr)           (*(volatile u16 *)(addr))
#define w16(addr, val)      (*(volatile u16 *)(addr) = (val))
#define r8(addr)            (*(volatile u8 *)(addr))
#define w8(addr, val)       (*(volatile u8 *)(addr) = (val))

#define TEST(value1 , value2)   ((value1 & value2) != 0)
#define BIT(n)              (1 << (n))

#define set_step(flag)      w32(STEP_BASE, flag)
#define set_sync(width)     w32(SYNC_BASE, width)

/*
 * Set and clear specified bits of a register.
 */
#define SET_BITS(addr, mask, val) { \
              u32 temp_reg_val = r32(addr); \
              temp_reg_val &= ~(mask); \
              temp_reg_val |= (val) & (mask); \
              w32(addr, temp_reg_val); \
          }

#define CLR_BITS(addr, mask) { \
              u32 temp_reg_val = r32(addr); \
              temp_reg_val &= ~(mask); \
              w32(addr, temp_reg_val); \
          }
/*
 * print u32 number directly by verilog $write function.
 */
#define newline()      w32(0x04200008, 0x0d0d0d0d)
#define printu32(val)  w32(0x04200000, (val))


/*
 * section define for test case
 */
typedef struct {
	void (*case_entry)(void);
	u32 case_num;
} module_test_case;

extern module_test_case _case_list_start;
extern module_test_case _case_list_end;

#define _case_init __attribute__((unused, section(".test_case")))

/*
 * global functions
 */
extern void printnum(u32 val);
extern void printreg(const charptr name, u32 addr);
extern void printval(const charptr name, unsigned int val);
extern void mprintf(const charptr str, ...);
extern void delayus(u32 us);
extern void delay_ms(u32 ms);
extern void call_test_case(u32 case_num);

#endif /* TYPES_H */

