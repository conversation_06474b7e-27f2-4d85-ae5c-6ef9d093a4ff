#include "uart.h"
#include "gic.h"
#include "types.h"

/* UART ISR */
void uart0_isr(){
    uart_puts("uart0_isr has been raised\n");
    uart_putc('\n');

    // 根据具体条件进行中断处理程序
    // 1. 接收错误
    if (*(volatile uint32_t *)UART_LSR & (UART_LSR_OE | UART_LSR_PE | UART_LSR_FE | UART_LSR_BI)) {
        // 处理接收错误
        /* 1. 接收缓冲区溢出错误 */
        if(*(volatile uint32_t *)UART_LSR & UART_LSR_OE){
            // 通知用户缓冲区溢出，及时读取数据
            uart_puts("UART RX FIFO Overload Error ! \n");
            uart_puts("Please use uart_getc to receive data ! \n");

            *(volatile uint32_t *)UART_MSR = 1; // 设置溢出进行时标志
        }
        
        // 接收错误复位应由仿真硬件自行处理，如缓冲区被读取时，硬件会自动清除错误标志
        // 但是如果没有读取缓冲区，错误标志不会被清除，需要手动清除
        *(volatile uint32_t *)UART_LSR &= ~(UART_LSR_OE | UART_LSR_PE | UART_LSR_FE | UART_LSR_BI);
    }

    // 2. 接收到一个完整字符或接收 FIFO 中有字符
    if (*(volatile uint32_t *)UART_IIR & UART_IIR_INT_RX_AVAIL) {
        uart_puts("int type is RCVR\n");
        // 处理接收逻辑
        // TODO

        // 清除中断标志
        *(volatile uint32_t *)UART_IIR &= ~UART_IIR_INT_RX_AVAIL;


    }

    // 3. 字符超时
    if (*(volatile uint32_t *)UART_IIR & UART_IIR_INT_TIMEOUT) {
        uart_puts("int type is overtime\n");
        // 处理字符超时逻辑
        // TODO

        // 清除中断标志
        *(volatile uint32_t *)UART_IIR &= ~UART_IIR_INT_TIMEOUT;

    }

    // 4. 发送缓存器空，或者发送 FIFO 中的字符数小于阈值
    if (*(volatile uint32_t *)UART_IIR & UART_IIR_INT_THR) {
        // uart_puts("int type is TX empty\n");
        // 处理发送缓存器空逻辑
        // TODO

        // 清除中断标志
        *(volatile uint32_t *)UART_IIR &= ~UART_IIR_INT_THR;
    }

    if(*(volatile uint32_t *)UART_FCR & UART_FCR_TET_EMPTY){
        // 处理FIFO 中的字符数小于阈值逻辑
        // TODO

        // 清除中断标志
        *(volatile uint32_t *)UART_IIR &= ~UART_FCR_TET_EMPTY;
    }

    // 5. 检测到 busy-TODO
    // 判断是否BUSY
    if (*(volatile uint32_t *)UART_IIR & UART_IIR_INT_BUSY) {
        // uart_puts("int type is busy\n");
        // 处理BUSY
        // TODO

        // 清除BUSY标志
        *(volatile uint32_t *)UART_IIR &= ~UART_IIR_INT_BUSY;

    }
}

// 串口输出字符
void uart_putc(int c)
{
    *(volatile uint32_t *)UART_TX = c; // 将字符写入发送寄存器
}

void uart1_putc(int c)
{
    *(volatile uint32_t *)UART1_TX = c; // 将字符写入发送寄存器
}

// 串口输出字符串
void uart_puts(const char *str)
{
    while (*str){
        uart_putc(*str++); // 按顺序发送字符串
    }
}

void uart1_puts(const char *str)
{
    while (*str){
        uart1_putc(*str++); // 按顺序发送字符串
    }
}


// 串口接收字符：使用UART1
char uart_getc()
{
    while((*(volatile uint32_t *)UART_LSR & UART_LSR_DR) == 0); // 等待接收数据准备好
    return *(volatile uint32_t *)UART_RX; // 从接收寄存器读取字符
}

// 串口接收字符串
void uart_gets(char *buffer, int size)
{
    int i = 0;
    while (i < size - 1){
        char c = uart_getc();
        // 遇到回车、换行、结束符就终止
        if (c == '\r' || c == '\n' || c == '\0'){
            break;
        }
        buffer[i++] = c;
    }
    buffer[i] = '\0';
}

// 设置 UART0 波特率
void UART0_SetBaudRate(uint32_t baudrate) {
    V_U32 *uart_ctrlR;			/*串口控制寄存器指针*/
    
    V_U32 *uart_baudDivdR;		/*串口波特率分频寄存器指针*/
    uint32_t divisor = SOC2018_UART0_CLK / (16 * baudrate); // 假设 UART 时钟为 50MHz

    uart_ctrlR = (V_U32 *)UART_LCR;
    uart_baudDivdR = (V_U32 *)UART_DLL;

    // 设置 DLAB 位以访问 DLL 和 DLH 寄存器
    *uart_ctrlR = *uart_ctrlR | 0x80;/*只有在bit 7位为1的情况下才能访问波特率设置寄存器*/

    // 设置 DLL 和 DLH 寄存器
	*uart_baudDivdR = divisor & 0xff;	/*低8位*/
	*(uart_baudDivdR + 1) = (divisor >> 8) & 0xff; /*高8位*/

    /*只有在bit 7位为0的情况下才能访问其它寄存器*/
	*uart_ctrlR = *uart_ctrlR & 0x7f;
    
}

// 使能 UART0 的 FIFO
void UART0_EnableFIFO(void) {
    *(volatile uint32_t *)UART_FCR |= UART_FCR_FIFOE;
}

// 禁用 UART0 的 FIFO
void UART0_DisableFIFO(void) {
    *(volatile uint32_t *)UART_FCR &= ~UART_FCR_FIFOE;
}

// 复位 UART0 的接收和发送 FIFO
void UART0_ResetFIFO(void) {
    *(volatile uint32_t *)UART_FCR |= UART_FCR_RFIFOR | UART_FCR_XFIFOR;
}

// 设置 UART0 数据长度
void UART0_SetDataLength(uint8_t length) {
    *(volatile uint32_t *)UART_LCR = (*(volatile uint32_t *)UART_LCR & 
    ~UART_LCR_DLS_MASK) | (length & UART_LCR_DLS_MASK);
}

// 禁用 UART0 中断
void UART0_DisableInterrupt(uint32_t interrupt_mask) {
    *(volatile uint32_t *)UART_IER &= ~interrupt_mask;
}

// 使能 UART0 中断
void UART0_EnableInterrupt(uint32_t interrupt_mask) {
    *(volatile uint32_t *)UART_IER |= interrupt_mask;
}

// 初始化 UART0
void UART0_Init(uint32_t baudrate) {
    // 配置波特率
    UART0_SetBaudRate(baudrate);
}
