#include "sram.h"
#include "gic.h"
#include "types.h"

extern int irq_count;

/* SRAM ISR */
void sram_obe_isr() {
    irq_count++;
    while(1) {

    }
}

void sram_tbe_isr() {
    irq_count++;
    while(1) {

    }
}

void MyBsp_CreateSramDataSEU(U32 addr) {
    
}

void MyBsp_CreateSramDataTBU(U32 addr) {

}

void MyBsp_CreateSramEdacSEU(U32 addr) {

}

void MyBsp_CreateSramEdacTBU(U32 addr) {

}

void MyBsp_SramSEUClear() {

}

void MyBsp_SramTBEClear() {

}
