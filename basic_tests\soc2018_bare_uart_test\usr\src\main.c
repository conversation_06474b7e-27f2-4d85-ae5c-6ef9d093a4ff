/*
 * 适用于 soc2018 的串口测试程序
 */
#include <stdlib.h>
#include <stdint.h>
#include <float.h>

#include "types.h"
#include "gic.h"
#include "sp804.h"
#include "uart.h"
#include "BSP_PRINT.h"

int main()
{
    /* GIC 初始化 */
    gic_init();

    int i;
    int sum = 0;
    int arr[] = {1, 10, 4, 5, 6, 7};
    int n = sizeof(arr) / sizeof(arr[0]);
    for (i = 0; i < 2; ++i){
        sum += arr[i];
    }
    double dbl = 1.5;
    double result = dbl * 2.5;

    // 串口测试
    uart_puts("this is uart test the start\n");
    uart_puts("this is uart drives output the start\n");
    uart_puts("this is uart drives output the end\n");
    uart_puts("this is uart test the end\n");

    char recv_temp = uart_getc();
    uart_puts("recv_temp: ");
    uart_putc(recv_temp);
    uart_putc('\n');

    char recv_buffer[32];
    uart_gets(recv_buffer, 32);
    uart_puts("recv_buffer: ");
    uart_puts(recv_buffer);
    uart_putc('\n');

    /* 格式输出函数测试 */
    print2("this is prin2 test\n");
	// 有符号整数输出测试
	print2("this is int %%d test\n");
    print2("sum = %d\n", sum);
	print2("this is int %%i test\n");
	print2("sum = %i\n", sum);
	// 无符号整数输出测试
	print2("this is unsigned int test\n");
	print2("unsigned int : %u\n", 1000);
    // float输出测试
	print2("this is float test\n");
    float f = 123456789.123456789;
    print2("float : f = %f\n", f);
	// 单精度浮点数（十进制）
	print2("this is float test\n");
	print2("Dec Float : %m\n", 123.456);
	// 单精度浮点数（科学计数法）
	print2("this is float %%e test\n");
	print2("Sci Float : %e\n", 123.456);
	// double输出测试
	print2("this is double %%F test\n");
	double d = 123456789.123456789;
	print2("double : d = %F\n", d);
	// 双精度浮点数（十进制）
	print2("this is double %%M test\n");
	print2("Dec Double : %M\n", 123.456);
	// 双精度浮点数（科学计数法）
	print2("this is double %%E test\n");
	print2("Sci Double : %E\n", 123.456);

    // 16进制输出测试
	print2("this is HEX %%x test\n");
    print2("1000 = 0x%x\n", 1000);
    // 10进制输出测试
	print2("this is DEC test\n");
    print2("1000 = %d\n", 1000);
    // 8进制输出测试
	print2("this is OCT test\n");
    print2("1000 = 0%o\n", 1000);
    // 字符串输出测试
    print2("this is a string test\n");
	print2("this is a string test : %s\n", "hello world");
	// 字符输出测试
	print2("this is a char test\n");
	print2("this is a char test : %c\n", 'A');
	// 指针输出测试
	print2("this is a pointer test\n");
	int *p = &sum;
	print2("this is a pointer test : %p\n", p);
	
    return 0;
}
