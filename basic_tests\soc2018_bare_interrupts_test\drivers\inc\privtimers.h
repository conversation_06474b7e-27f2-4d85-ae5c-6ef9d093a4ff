/*
 * Cortex-A9 内核 Timer 驱动文件
 */

#include <stdint.h>

// a9mpcore 基地址
#define PERIPHBASE 0x3FFF0000

// Global Timer 寄存器定义
#define GLOBAL_TIMER_BASE           (PERIPHBASE + 0x200)
#define GL<PERSON><PERSON>L_TIMER_COUNTER_LOW    (GLOBAL_TIMER_BASE + 0x00)
#define GLOBAL_TIMER_COUNTER_HIGH   (GLOBAL_TIMER_BASE + 0x04)
#define GLOBAL_TIMER_CTLR           (GLOBAL_TIMER_BASE + 0x08)
#define GLOBAL_TIMER_ISR            (GLOBAL_TIMER_BASE + 0x0C)
#define GLOBAL_TIMER_CMP_LOW        (GLOBAL_TIMER_BASE + 0x10)
#define GLOBAL_TIMER_CMP_HIGH       (GLOBAL_TIMER_BASE + 0x14)
#define GLOBAL_TIMER_AUTO_INCR      (GLOBAL_TIMER_BASE + 0x18)

// Private Timer 寄存器定义
#define PRIVATE_TIMER_BASE          (PERIPHBASE + 0x600)
#define PRIVATE_TIMER_LOAD          (PRIVATE_TIMER_BASE + 0x00)
#define PRIVATE_TIMER_COUNTER       (PRIVATE_TIMER_BASE + 0x04)
#define PRIVATE_TIMER_CTLR          (PRIVATE_TIMER_BASE + 0x08)
#define PRIVATE_TIMER_ISR           (PRIVATE_TIMER_BASE + 0x0C)

// Watchdog Timer 寄存器定义
#define WDT_BASE                    (PERIPHBASE + 0x620)
#define WDT_LOAD                    (WDT_BASE + 0x00)
#define WDT_COUNTER                 (WDT_BASE + 0x04)
#define WDT_CTRL                    (WDT_BASE + 0x08)
#define WDT_ISR                     (WDT_BASE + 0x0C)
#define WDT_RESET                   (WDT_BASE + 0x10)
#define WDT_DISABLE                 (WDT_BASE + 0x14)

/*
 * 测试任务
 */
void global_timer_count_test();
void global_timer_compare_test();
void private_timer_count_test();
void private_timer_load_test();
void watchdog_count_test();
void watchdog_irq_test();
void watchdog_reset_test();
