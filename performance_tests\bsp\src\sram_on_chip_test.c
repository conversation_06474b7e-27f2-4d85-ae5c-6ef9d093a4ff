
#include "types.h"
#include "soc502_parameter.h"
#include "sram.h"
#include "uart.h"

///////////////////////////////////////////////
// test case
///////////////////////////////////////////////

// UART0.tx->UART1.rx
// UART1.tx->UART0.rx

#define SRAM_INTERRUPT_TEST 0


void sram_dancuo_irq_handler(void)
{
	print2("\r\n\r\nSRAM Single Err IRQ");
	
	print2("\r\nSingle err addr = 0x%x",*(volatile unsigned int *)(0x40200010));
	print2("\r\nSingle err cnt = %x",*(volatile unsigned int *)(0x40200018));
	print2("\r\nByte err reg = %x",*(volatile unsigned int *)(0x40200040));

	*(volatile unsigned int *)(0x40200030) = 0x00000001;//clear one bit err
}
void sram_duocuo_irq_handler(void)
{
	u32 data;
	print2("\r\nSRAM Double Err IRQ");
	print2("\r\nDOUBLE_ERR_CTL_REG is 0x%x ",r32(0xA00E0000));//Double err ctrl reg
	
	print2("\r\nDouble err addr = 0x%x, 0x%x",*(volatile unsigned int *)(0x40200020),*(volatile unsigned int *)(0x40200024));
	print2("\r\nByte err reg = %x",*(volatile unsigned int *)(0x40200040));
	print2("\r\nData_two_bit_reg = %x, %x",*(volatile unsigned int *)(0x40200058),*(volatile unsigned int *)(0x4020005c));
	print2("\r\nEdac_two_bit_reg = %x, %x",*(volatile unsigned int *)(0x40200060),*(volatile unsigned int *)(0x40200064));

	*(volatile unsigned int *)(0x40200038) = 0x00000001;//clear duocuo flag

}

void sram_on_chip_regtest(void)
{
	unsigned int j,addr,data;

	print2("\r\n------Sram on chip register test------\r\n");
	
	for (j=0;j<0x30;j+=0x8)
	{
		addr = 0x40200000+j;	
		print2("\r\n addr: 0x%x =  0x %x",addr,*(volatile unsigned int *)(addr));
			
	}
		j=0x2c;
		addr = 0x40200000+j;	
		print2("\r\n addr: 0x%x =  0x %x",addr,*(volatile unsigned int *)(addr));
	for (j=0x30;j<0x68;j+=0x8)
	{
		addr = 0x40200000+j;	
		print2("\r\n addr: 0x%x =  0x %x",addr,*(volatile unsigned int *)(addr));
			
	}

}

void sram_on_chip_rw_test(u32 startaddr, u32 size)
{
	unsigned int i,j,k,addr,data;
	u32 sram_data[4] ={0xffffffff, 0x0, 0x55aaaa55, 0x5aa55aa5};
	

	for(i=0;i<2;i++)
	{
		if((i%2)==0)
		{
			*(volatile unsigned int *)(0x40200000) = 0x00000000;//disable edac
			print2("\r\nDisable EDAC");
		}
		else
		{
			*(volatile unsigned int *)(0x40200000) = 0x00000001;//enable edac
			gic_register_irq_entry(80,sram_dancuo_irq_handler);
			gic_enable_irq(80);
			print2("\r\nEnable EDAC");
		}

		for(k=0;k<4;k++)
		{
			for (j=0x0;j<0x100000;j+=4)
				*(volatile unsigned int *)(0x40100000+j) = sram_data[k];

			for (j=0x0;j<0x100000;j+=4)
			{
				addr = 0x40100000+j;	
				data = *(volatile unsigned int *)(addr) ;
				if(data != sram_data[k])
				{
					print2("Error addr: %x,    data: %x,   right:%x\r\n",addr,data,sram_data[k]);
				}
			}
		}		
	}

}

void sram_on_chip_bypass_test(void)
{
unsigned int j,addr;
unsigned int rdata,cmp_data;

	print2("\r\n------Sram on chip bypass test------\r\n");

	gic_disable_irq(80);
	*(volatile unsigned int *)(0x40200000) = 0x00000009;//enable edac


for(j=0 ;j<100;j++)		
{
	*(volatile unsigned int *)(0x40200000) = 0x00000004;//enable err insert
	 addr = 0x40100000+j*0x4;	
	 *(volatile unsigned int *)(0x40200050) = addr;//edac_write_bypass_addr
	*(volatile unsigned int *)(0x40200008) = (0x01020304+0x01010101*j);//edac_data  //double err
	*(volatile unsigned int *)(addr) = 0x11223344;//edac_data
}

for(j=0 ;j<100;j++)		
{
	*(volatile unsigned int *)(0x40200000) = 0x00000002;//enable err insert
	 addr = 0x40100000+j*0x4;
	 *(volatile unsigned int *)(0x40200048) = addr;//enable err insert
	 rdata = *(volatile unsigned int *)(addr);
	cmp_data = (0x01020304+0x01010101*j);
	cmp_data = cmp_data&0x1f1f1f1f;

	 if(j%2==0)
	 {
	 rdata = *(volatile unsigned int *)(0x40200028);
		  if (rdata != cmp_data)//edac_data  //double err	
	  	{
			print2("\nERROR!- sram on chip bypass test error rdata = 0x%x ,except value = 0x%x -\n",rdata,cmp_data);
		}
	}
	else
	{
		rdata = *(volatile unsigned int *)(0x4020002c);
	  	if (rdata != cmp_data)//edac_data  //double err	
  		{
	  		print2("\nERROR!- sram on chip bypass test error rdata = 0x%x ,except value = 0x%x -\n",rdata,cmp_data);
  		}
	}


}

}

void sram_on_chip_dancuo_test(void)
{
unsigned int j,addr;
unsigned int rdata;
unsigned short rdata_s;
unsigned char rdata_c;
//0x11223344
//0x44 --0x1e
//0x33 --0x0c
//0x22 --0x17
//0x11 --0x1b
//*(volatile unsigned int *)(0x40200010) = 0x1b170c0e;//edac_data sig err
	print2("\r\n------Sram on chip single error test------\r\n");

#if SRAM_INTERRUPT_TEST
gic_register_irq_entry(80,sram_dancuo_irq_handler);
gic_enable_irq(80);
#else
gic_disable_irq(80);
*(volatile unsigned int *)(0x40200000) = 0x00000009;//enable edac
#endif


	  print2("\r\nint dan cuo no interrupt here begin ");		

for(j=0 ;j<4;j++)		
{
	addr = 0x40100000+j*0x400;	
	  print2("\r\n\r\ntest addr = 0x%x  ",addr);		

	rdata= (0x1b170c1e ^ (0x10<< (j*8)));
	  print2("\r\nedac  = 0x%x  ",rdata);		
	
	*(volatile unsigned int *)(0x40200008) = (0x1b170c1e ^ (0x10<< (j*8)));//edac_data  --single error
	*(volatile unsigned int *)(0x40200050) = addr;//edac_write_bypass_addr
	*(volatile unsigned int *)(0x40200000) = 0x00000004;//enable err insert
	*(volatile unsigned int *)(addr) = 0x11223344;//edac_data
	*(volatile unsigned int *)(0x40200000) = 0x00000001;//enable edac

	rdata = *(volatile unsigned int *)(addr) ;
	if(rdata != 0x11223344)
	{
		print2("\r\nError1 addr: %x,    data: %x",addr,*(volatile unsigned int *)(addr));
	}

	delay_ms(100);
	print2("\r\nSingle err addr = 0x%x",*(volatile unsigned int *)(0x40200010));
	print2("\r\nSingle err cnt = %x",*(volatile unsigned int *)(0x40200018));
	print2("\r\nByte err reg = %x",*(volatile unsigned int *)(0x40200040));
	*(volatile unsigned int *)(0x40200030) = 0x00000001;//clear one bit err
	
	  print2("\r\n\r\nstep 2  ");		
	 *(volatile unsigned int *)(addr) = rdata+1;
	if(*(volatile unsigned int *)(addr) != 0x11223345)//
		{
			print2("\r\nError2 addr: %x,    data: %x",addr,*(volatile unsigned int *)(addr));
		}

	delay_ms(100);
	print2("\r\nSingle err addr = 0x%x",*(volatile unsigned int *)(0x40200010));
	print2("\r\nSingle err cnt = %x",*(volatile unsigned int *)(0x40200018));
	print2("\r\nByte err reg = %x",*(volatile unsigned int *)(0x40200040));

}

	  print2("\r\nint dan cuo  no interrupt here end \r\n ");	


	print2("\r\n\r\n\r\nshort dan cuo no interrupt here begin ");		

for(j=0 ;j<4;j++)		
{
	addr = 0x40100012+j*0x400;				
	  print2("\r\n\r\ntest addr = 0x%x  ",addr);		

	*(volatile unsigned int *)(0x40200008) = (0x1b170c1e ^ (0x10<< (j*8)));//edac_data  //double err
	*(volatile unsigned int *)(0x40200050) = (addr-0x2);//edac_write_bypass_addr
	*(volatile unsigned int *)(0x40200000) = 0x00000004;//enable err insert
	*(volatile unsigned int *)(addr-0x2) = 0x11223344;//edac_data
	*(volatile unsigned int *)(0x40200000) = 0x00000001;//enable edac
	rdata = *(volatile unsigned short *)(addr) ;
	 if(rdata!= 0x1122)//why 0x1122 not 0x3344?yww
	{
		print2("\r\nError3 addr: %x,    data: %x",addr,*(volatile unsigned char *)(addr));
	}
	 
	delay_ms(100);
	print2("\r\nSingle err addr = 0x%x",*(volatile unsigned int *)(0x40200010));
	print2("\r\nSingle err cnt = %x",*(volatile unsigned int *)(0x40200018));
	print2("\r\nByte err reg = %x",*(volatile unsigned int *)(0x40200040));
	*(volatile unsigned int *)(0x40200030) = 0x00000001;//clear one bit err
	
	
	  print2("\r\n\r\nstep 2  ");		
	 *(volatile unsigned short *)(addr) = rdata+1;
	 if(*(volatile unsigned short *)(addr) != 0x1123)
	{
		print2("\r\nError4 addr: %x,    data: %x",addr,*(volatile unsigned char *)(addr));
	}

	delay_ms(100);
	print2("\r\nSingle err addr = 0x%x",*(volatile unsigned int *)(0x40200010));
	print2("\r\nSingle err cnt = %x",*(volatile unsigned int *)(0x40200018));
	print2("\r\nByte err reg = %x",*(volatile unsigned int *)(0x40200040));

}
	print2("\r\nshort dan cuo no interrupt here end \r\n");	

	print2("\r\n\r\n\r\nchar dan cuo no interrupt here begin");		

for(j=0 ;j<4;j++)		
{
	addr = 0x40100023+j*0x400;
	
	*(volatile unsigned int *)(0x40200008) = (0x1b170c1e ^ (0x10<< (j*8)));//edac_data  //double err
	*(volatile unsigned int *)(0x40200050) = (addr-0x3);//edac_write_bypass_addr
	*(volatile unsigned int *)(0x40200000) = 0x00000004;//enable err insert
	*(volatile unsigned int *)(addr-0x3) = 0x11223344;//edac_data
	*(volatile unsigned int *)(0x40200000) = 0x00000001;//enable edac

	rdata = *(volatile unsigned char *)(addr);
	if( rdata != 0x11)//
	{
		print2("\r\nError5 addr: %x,    data: %x",addr,*(volatile unsigned char *)(addr));
	}

	
	delay_ms(100);
	print2("\r\nSingle err addr = 0x%x",*(volatile unsigned int *)(0x40200010));
	print2("\r\nSingle err cnt = %x",*(volatile unsigned int *)(0x40200018));
	print2("\r\nByte err reg = %x",*(volatile unsigned int *)(0x40200040));
	*(volatile unsigned int *)(0x40200030) = 0x00000001;//clear one bit err

	  print2("\r\n\r\nstep 2  ");		
	 *(volatile unsigned char *)(addr) = rdata+1;
 	if(*(volatile unsigned char *)(addr) != 0x12)
	{
		print2("\r\nError6 addr: %x,    data: %x",addr,*(volatile unsigned char *)(addr));
	}

	delay_ms(100);
	print2("\r\nSingle err addr = 0x%x",*(volatile unsigned int *)(0x40200010));
	print2("\r\nSingle err cnt = %x",*(volatile unsigned int *)(0x40200018));
	print2("\r\nByte err reg = %x",*(volatile unsigned int *)(0x40200040));

}
	  print2("\r\nchar dan cuo no interrupt here end \r\n");	


	  print2("\r\nclear dan cuo cnt ");	
	*(volatile unsigned int *)(0x40200018) = 0x00000000;//edac_data  //double err
	  print2("\r\nSingle err cnt = %x",*(volatile unsigned int *)(0x40200018));

	print2("\r\n------Sram on chip single error test  end------\r\n");

}

void sram_on_chip_duocuo_test(void)
{
unsigned int j,addr;
unsigned int rdata;
unsigned short rdata_s;
unsigned char rdata_c;

print2("\r\n------Sram on chip double error test------\r\n");


//32bit
for(j=0 ;j<1;j++)		
{
	addr = 0x40100030+j*0x400;	

	  print2("\r\n\r\ntest addr = 0x%x  ",addr);		
	  print2("\r\nedac  = 0x1b170c0f  ");		

	*(volatile unsigned int *)(0x40200008) = 0x1b170c0f;//edac_data  //double err
	*(volatile unsigned int *)(0x40200050) = addr;//edac_write_bypass_addr
	*(volatile unsigned int *)(0x40200000) = 0x00000004;//enable err insert
	*(volatile unsigned int *)(addr) = 0x11223344;//edac_data
#if SRAM_INTERRUPT_TEST
	*(volatile unsigned int *)(0x40200000) = 0x00000001;//enable edac and hresp
#else
	*(volatile unsigned int *)(0x40200000) = 0x00000009;//enable edac
#endif

	rdata = (*(volatile unsigned int *)(addr));
	 print2("\r\n\r\nint 0 double error hou du chu shuju is: %x ",rdata);
	 
	print2("\r\nDouble err addr = 0x%x",*(volatile unsigned int *)(0x40200020));
	print2("\r\nByte err reg = %x",*(volatile unsigned int *)(0x40200040));
	print2("\r\nData_two_bit_reg = %x",*(volatile unsigned int *)(0x40200058));
	print2("\r\nEdac_two_bit_reg = %x",*(volatile unsigned int *)(0x40200060));
	 *(volatile unsigned int *)(0x40200038) = 0x00000001;//clear duocuo flag


	 *(volatile unsigned int *)(addr) = rdata+1;
	 if(*(volatile unsigned int *)(addr) != (rdata+1))
	{
		print2("\r\nError8 addr: %x,    data: %x\r\n",addr,*(volatile unsigned int *)(addr));
	}
	 
	print2("\r\n\r\nDouble err addr = 0x%x",*(volatile unsigned int *)(0x40200020));
	print2("\r\nByte err reg = %x",*(volatile unsigned int *)(0x40200040));
	print2("\r\nData_two_bit_reg = %x",*(volatile unsigned int *)(0x40200058));
	print2("\r\nEdac_two_bit_reg = %x",*(volatile unsigned int *)(0x40200060));


}


for(j=0 ;j<1;j++)		
{
	addr = 0x40100034+j*0x400;
	
	  print2("\r\n\r\ntest addr = 0x%x  ",addr);		
	  print2("\r\nedac  = 0x1b170f1e  ");		

	*(volatile unsigned int *)(0x40200008) = 0x1b170f1e;//edac_data  //double err
	*(volatile unsigned int *)(0x40200050) = addr;//edac_write_bypass_addr
	*(volatile unsigned int *)(0x40200000) = 0x00000004;//enable err insert
	*(volatile unsigned int *)(addr) = 0x11223344;//edac_data
#if SRAM_INTERRUPT_TEST
	*(volatile unsigned int *)(0x40200000) = 0x00000001;//enable edac and hresp
#else
	*(volatile unsigned int *)(0x40200000) = 0x00000009;//enable edac
#endif

	rdata = *(volatile unsigned int *)(addr);
	 print2("\r\n\r\nint 0 double error hou du chu shuju is: %x ",rdata);
/*	 
	print2("\r\nDouble err addr = 0x%x",*(volatile unsigned int *)(0x40200020));
	print2("\r\nByte err reg = %x",*(volatile unsigned int *)(0x40200040));
	print2("\r\nData_two_bit_reg = %x",*(volatile unsigned int *)(0x40200058));
	print2("\r\nEdac_two_bit_reg = %x",*(volatile unsigned int *)(0x40200060));
	*(volatile unsigned int *)(0x40200038) = 0x00000001;//clear duocuo flag
*/


	 *(volatile unsigned int *)(addr) = rdata+1;
	 if(*(volatile unsigned int *)(addr) != (rdata+1))//why 0x55667788? yww
	{
		print2("\r\nError8 addr: %x,    data: %x\r\n",addr,*(volatile unsigned int *)(addr));
	}
	 
	print2("\r\n\r\nDouble err addr = 0x%x, 0x%x",*(volatile unsigned int *)(0x40200020),*(volatile unsigned int *)(0x40200024));
	print2("\r\nByte err reg = %x",*(volatile unsigned int *)(0x40200040));
	print2("\r\nData_two_bit_reg = %x, %x",*(volatile unsigned int *)(0x40200058),*(volatile unsigned int *)(0x4020005c));
	print2("\r\nEdac_two_bit_reg = %x, %x",*(volatile unsigned int *)(0x40200060),*(volatile unsigned int *)(0x40200064));


}

	  print2("\r\n\r\nDisable edac \r\n ");	
*(volatile unsigned int *)(0x40200000) = 0x00000000;//disable edac
for(j=0 ;j<1;j++)		
{
	addr = 0x40100060+j*0x400;	

	  print2("\r\n\r\ntest addr = 0x%x  ",addr);		
	  print2("\r\nedac  = 0x1b170c0f  ");		

	*(volatile unsigned int *)(0x40200008) = 0x1b170c0f;//edac_data  //double err
	*(volatile unsigned int *)(0x40200050) = (addr);//edac_write_bypass_addr
	*(volatile unsigned int *)(0x40200000) = 0x00000004;//enable err insert
	*(volatile unsigned int *)(addr) = 0x11223344;//edac_data
	*(volatile unsigned int *)(0x40200000) = 0x00000000;//enable edac


	rdata = *(volatile unsigned int *)(addr);
	 print2("\r\nint 0 double error hou du chu shuju is: %x \n",rdata);

//	print2("int shuang cuo no interrupt here begin \r\n ");		
	//rdata = *(volatile unsigned int *)(addr) ;
	 *(volatile unsigned int *)(addr) = rdata+1;
	 if(*(volatile unsigned int *)(addr) != (rdata+1))//should be 0x11223345? yww
		{
			print2("\r\nError8 addr: %x,    data: %x\r\n",addr,*(volatile unsigned int *)(addr));
		}
	//  print2("int shuang cuo  no interrupt here end \r\n ");	
}
/*
//16bit
for(j=0 ;j<1;j++)		
{
	addr = 0x40100042+j*0x400;	
	*(volatile unsigned int *)(0x40200008) = 0x18170c1e;//edac_data  //double err
	*(volatile unsigned int *)(0x40200050) = (addr-0x2);//edac_write_bypass_addr
	*(volatile unsigned int *)(0x40200000) = 0x00000004;//enable err insert
	*(volatile unsigned int *)(addr-0x2) = 0x11223344;//edac_data
	*(volatile unsigned int *)(0x40200000) = 0x00000001;//enable edac
	
	print2("short 0 double error hou du chu shuju is: %x \n",*(volatile unsigned short *)(addr));	
	
	print2("short double cuo no interrupt here begin \n ");		
	rdata_s = *(volatile unsigned short *)(addr) ;
	*(volatile unsigned short *)(addr) = rdata_s+1;
	  if(*(volatile unsigned short *)(addr) != 0x5567)
	{
		print2("\r\nError5 addr: %x,    data: %x\r\n",addr,*(volatile unsigned short *)(addr));
	}
		 
		 
	print2("short double cuo no interrupt here end \n ");	
}

for(j=0 ;j<1;j++)		
{
	addr = 0x40100046+j*0x400;	
	*(volatile unsigned int *)(0x40200008) = 0x1817001e;//edac_data  //double err
	*(volatile unsigned int *)(0x40200050) = (addr-0x2);//edac_write_bypass_addr
	*(volatile unsigned int *)(0x40200000) = 0x00000004;//enable err insert
	*(volatile unsigned int *)(addr-0x2) = 0x11223344;//edac_data
	*(volatile unsigned int *)(0x40200000) = 0x00000001;//enable edac
	
	print2("short 1 double error hou du chu shuju is: %x \n",*(volatile unsigned short *)(addr-0x2));	
	
	print2("short double cuo no interrupt here begin \n ");		
	rdata_s = *(volatile unsigned short *)(addr-0x2) ;
	*(volatile unsigned short *)(addr-0x2) = rdata_s+1;
	  if(*(volatile unsigned short *)(addr-0x2) != 0x7789)
	{
		print2("\r\nError5 addr: %x,    data: %x\r\n",addr,*(volatile unsigned short *)(addr));
	}
		 
	print2("short double cuo no interrupt here end \n ");	
	
}


//8bit
for(j=0 ;j<1;j++)		
{
	addr = 0x40100053+j*0x400;	
	*(volatile unsigned int *)(0x40200008) = 0x18170c1e;//edac_data  //double err
	*(volatile unsigned int *)(0x40200050) = (addr-0x3);//edac_write_bypass_addr
	*(volatile unsigned int *)(0x40200000) = 0x00000004;//enable err insert
	*(volatile unsigned int *)(addr-0x3) = 0x11223344;//edac_data
	*(volatile unsigned int *)(0x40200000) = 0x00000001;//enable edac
			
	rdata_c = *(volatile unsigned char *)(addr) ;	
				
	print2("char 0 double error hou du chu shuju is: %x \n",rdata_c);		
			

	print2("char double cuo no interrupt here begin \n ");		
	rdata_c = *(volatile unsigned char *)(addr) ;
	 *(volatile unsigned char *)(addr) = rdata_c+1;
 	if(*(volatile unsigned char *)(addr) != 0x56)
	{
		print2("\r\nError6 addr: %x,    data: %x\r\n",addr,*(volatile unsigned char *)(addr));
	}
	print2("char double cuo no interrupt here end \n ");	
}

print2("no intrupt beween here --begin-------------33------------ \n");	
for(j=0 ;j<1;j++)	///buhuichufazhongduan 	
{
	addr = 0x40100061+j*0x400;	
	*(volatile unsigned int *)(0x40200008) = 0x18170c1e;//edac_data  //double err
	*(volatile unsigned int *)(0x40200050) = (addr-0x1);//edac_write_bypass_addr
	*(volatile unsigned int *)(0x40200000) = 0x00000004;//enable err insert
	*(volatile unsigned int *)(addr-0x1) = 0x11223344;//edac_data
	*(volatile unsigned int *)(0x40200000) = 0x00000001;//enable edac
			
	rdata_c = *(volatile unsigned char *)(addr) ;	
				
	print2("char 1 double error hou du chu shuju is: %x \n",rdata_c);		
			
	rdata_c = *(volatile unsigned char *)(addr) ;
	 *(volatile unsigned int *)(addr-0x1) = rdata_c*0x100;
 	if(*(volatile unsigned char *)(addr) != 0x33)
	{
		print2("\r\nError6 addr: %x,    data: %x\r\n",addr,*(volatile unsigned char *)(addr));
	}
}
print2("no intrupt beween here --end------------------------- \n");	

for(j=0 ;j<1;j++)		
{
	addr = 0x40100065+j*0x400;	
	*(volatile unsigned int *)(0x40200008) = 0x18171d1e;//edac_data  //double err
	*(volatile unsigned int *)(0x40200050) = (addr-0x1);//edac_write_bypass_addr
	*(volatile unsigned int *)(0x40200000) = 0x00000004;//enable err insert
	*(volatile unsigned int *)(addr-0x1) = 0x11223344;//edac_data
	*(volatile unsigned int *)(0x40200000) = 0x00000001;//enable edac

			
	rdata_c = *(volatile unsigned char *)(addr) ;	
				
	print2("char 2 double error hou du chu shuju is: %x \n",rdata_c);			

	print2("char double cuo no interrupt here begin \n ");		
	rdata_c = *(volatile unsigned char *)(addr) ;
	 *(volatile unsigned char *)(addr) = rdata_c+1;
 	if(*(volatile unsigned char *)(addr) != 0x78)
	{
		print2("\r\nError6 addr: %x,    data: %x\r\n",addr,*(volatile unsigned char *)(addr));
	}
	print2("char double cuo no interrupt here end \n ");	
}

*/
}



