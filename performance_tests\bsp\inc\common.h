
#ifndef __COMMON_H__
#define __COMMON_H__

#include <stdint.h>

#define __IO volatile

#define io_write_8(a,d)   ( *(__IO uint8_t*)(a) = (d) )
#define io_read_8(a)      ( *(__IO uint8_t*)(a) )
#define io_write_16(a,d)   ( *(__IO uint16_t*)(a) = (d) )
#define io_read_16(a)      ( *(__IO uint16_t*)(a) )
#define io_write_32(a,d)  ( *(__IO uint32_t*)(a) = (d) )
#define io_read_32(a)     ( *(__IO uint32_t*)(a) )
#define io_write_64(a,d)  ( *(__IO uint64_t*)(a) = (d) )
#define io_read_64(a)     ( *(__IO uint64_t*)(a) )


//-------------------------------------------
//
// Two method to get A9 private periph base addr :
//    1. manual set A9 private periph base addr
#ifdef MMU_TEST
#define PERIPH_BASE_ADDR        0x00100000    // VA
#else
#define PERIPH_BASE_ADDR       (0xFFA00000UL)    // PA
#endif
//    2. use below function (just for debug)
uint32_t get_periph_base_addr(void);

//reset control physical base addr
#define RST_CTL_BASE           (0xFFBD0000UL)

//clock control physical base addr
#define CLK_CTL_BASE           (0xFFBE0000UL)

//system control physical base addr
#define SYS_CTL_BASE           (0xFFBF0000UL)

//ACP map physical base addr
#define ACP_MAP_BASE           (0xFFBC0000UL)

//DMA controller physical base addr
#define  DMAC_BASE             (0xA0091000UL)////(0xFFB60000UL) ARM APB s09
#define  DMAC_CONFIG_BASE      (0xFFBF0700UL)

//DDR_MCTL base addr
#define  DDR_MCTL_BASE         (0xFE000000UL)
#define  DDR_PUBL_BASE         (0xFE001000UL)

//GPIO base addr
#define GPIO_0_BASE_ADDR    (0xFFB50000UL)
#define GPIO_1_BASE_ADDR    (0xFFB51000UL)

//for private-timer/watch-dog/
#define PERIPHCLK             (12500000UL)   // 12.5MHz
   
//uart clocl and physical base addr
#define UART_CLK              (25000000UL)   // 25MHz
#define UART_BASE_PTR         (0xFE020000UL)
#define BAUD_RATE             (115200)
   
//spi clocl and physical base addr
#define SPI_BASE_ADDR         (0xFFB00000UL)
#define SPI_BOOT_BASE_ADDR    (0xFE041000UL)
#define SPI_BOOT_CLK          (12500000UL)
#define W25Q32_CLK            (6250000UL)

//------------------------------------------
//
// Get CPU ID
unsigned int get_CPUID(void);

//
// Get CPU Secure state
//
uint32_t
get_CPU_secure_state(void);

//
// enable division by zero exception
//
void 
enable_div0_exception(void);

#define MMU_COHERENT    (0x00011DEEUL)
#define MMU_DEVICE      (0x00010DF6UL)
#define MMU_TTB_BASE    (0x00040000UL)
/*remap pa to va*/
void
MMU_Remap(uint32_t va, uint32_t pa, uint32_t attribute);



//
// set vector base address
//
void
set_vector(uint32_t addr);

//------------------------------------------
// Must locate at file end
//
#include<stdio.h>

#include "tube.h"
#include "gic.h"
#include "interrupt.h"
#include "uart1.h"
#include "clkctl.h"
#include "reset.h"
#include "v7.h"
#include "MP_Mutexes.h"
#include "sysctl.h"

#pragma diag_suppress 111

#endif


