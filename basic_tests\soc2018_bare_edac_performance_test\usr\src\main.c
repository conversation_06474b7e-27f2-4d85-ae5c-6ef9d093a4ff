/*
 * 此处填写用户程序业务逻辑描述
 */

#include "types.h"
#include "gic.h"        /* 中断控制功能 */
#include "bsp_print.h"  /* 串口输出功能 */
#include "sram.h"
#include "sdram.h"
#include "sp804.h"
#include <stdlib.h>

extern int irq_count;

#define TEST_RUNS 10000
#define PMU_WRITE_PMCR(v)       __asm__ volatile ("MCR p15, 0, %0, c9, c12, 0" :: "r"(v))
#define PMU_READ_PMCR(out)      __asm__ volatile ("MRC p15, 0, %0, c9, c12, 0" : "=r"(out))
#define PMU_WRITE_PMCNTENSET(v) __asm__ volatile ("MCR p15, 0, %0, c9, c12, 1" :: "r"(v))
#define PMU_WRITE_PMUSERENR(v)  __asm__ volatile ("MCR p15, 0, %0, c9, c14, 0" :: "r"(v))
#define PMU_READ_PMCCNTR(out)   __asm__ volatile ("MRC p15, 0, %0, c9, c13, 0" : "=r"(out))

static void pmu_init_cycle_counter(void)
{
    uint32_t pmcr;

    /* 1读取当前 PMCR → 置 E|P|C (bit0|1|2)，同时确保 D=0（不分频） */
    PMU_READ_PMCR(pmcr);
    pmcr |= (1U << 0) | (1U << 1) | (1U << 2);   /* E P C */
    pmcr &= ~(1U << 3);                          /* D = 0 */
    PMU_WRITE_PMCR(pmcr);

    /* 启用周期计数器 (PMCNTENSET bit31) */
    PMU_WRITE_PMCNTENSET(1U << 31);

    /* 允许用户态读 PMCCNTR：PMUSERENR.EN=1 */
    PMU_WRITE_PMUSERENR(1U);

    __asm__ volatile ("ISB");
}

static inline uint32_t read_pmccntr(void)
{
    uint32_t cc;
    PMU_READ_PMCCNTR(cc);
    return cc;
}

#define SRAM_BASE_ADDR    ((volatile uint32_t *)0x40000000U)
#define SRAM_SIZE_BYTES   (2 * 1024 * 1024)
#define SRAM_WORD_COUNT   (SRAM_SIZE_BYTES / sizeof(uint32_t))

#define SDRAM_BASE_ADDR   ((volatile uint32_t *)0xC0000000U)
#define SDRAM_WORD_COUNT  (1 * 1024 * 1024 / sizeof(uint32_t))

void test_function_1(void)
{
    volatile uint32_t *addr = SRAM_BASE_ADDR;
    *addr = 0xFFFFFFFF;
    uint32_t data = *addr; 
    (void)data;
}

void test_function_2(void)
{
    // 整块写
    volatile uint32_t *addr = SRAM_BASE_ADDR;
    for (uint32_t i = 0; i < SRAM_WORD_COUNT; i++) {
        *addr++ = 0xFFFFFFFF;
    }
    addr = SRAM_BASE_ADDR;
    for (uint32_t i = 0; i < SRAM_WORD_COUNT; i++) {
        uint32_t data = *addr++;
        (void)data;
    }
}

void test_function_3(void)
{
    volatile uint32_t *addr = SDRAM_BASE_ADDR;
    for (uint32_t i = 0; i < SDRAM_WORD_COUNT; i++) {
        *addr++ = 0xFFFFFFFF;
    }
    addr = SDRAM_BASE_ADDR;
    for (uint32_t i = 0; i < SDRAM_WORD_COUNT; i++) {
        uint32_t data = *addr++;
        (void)data;
    }
}

void test_function_4(void)
{
    volatile uint32_t *addr = SRAM_BASE_ADDR;
    for (uint32_t i = 0; i < SRAM_WORD_COUNT; i++) {
        *addr = 0xFFFFFFFF;
        uint32_t data = *addr++;
        (void)data;
    }
}

void test_function_5(void)
{
    volatile uint32_t *addr = SRAM_BASE_ADDR + (SRAM_WORD_COUNT - 1);
    for (uint32_t i = 0; i < SRAM_WORD_COUNT; i++) {
        *addr-- = 0xFFFFFFFF;
    }
    addr = SRAM_BASE_ADDR + (SRAM_WORD_COUNT - 1);
    for (uint32_t i = 0; i < SRAM_WORD_COUNT; i++) {
        uint32_t data = *addr--;
        (void)data;
    }
}

void test_function_6(void)
{
    volatile uint32_t *base = SRAM_BASE_ADDR;

    srand(read_pmccntr());

    for (uint32_t i = 0; i < 100; i++) {
        uint32_t idx = rand() % SRAM_WORD_COUNT;
        base[idx] = 0xFFFFFFFF;
    }

    srand(read_pmccntr());
    for (uint32_t i = 0; i < 100; i++) {
        uint32_t idx = rand() % SRAM_WORD_COUNT;
        uint32_t data = base[idx];
        (void)data;
    }
}

void test_function_7(void)
{
    volatile uint32_t *base = SRAM_BASE_ADDR;

    srand(read_pmccntr());

    for (uint32_t i = 0; i < 10; i++) {
        uint32_t idx = rand() % SRAM_WORD_COUNT;
        base[idx] = 0xFFFFFFFF;
    }

    srand(read_pmccntr());
    for (uint32_t i = 0; i < 10; i++) {
        uint32_t idx = rand() % SRAM_WORD_COUNT;
        uint32_t data = base[idx];
        (void)data;
    }
}

void through_pmccntr(void) {
    pmu_init_cycle_counter();

    uint64_t sum_cycles = 0;

    for (int i = 0; i < TEST_RUNS; i++)
    {
        /* ========== 测量窗口开始 ========== */
        uint32_t saved_cpsr;
        __asm__ volatile (
            "MRS %[cpsr], CPSR\n\t"
            "CPSID i\n\t"                /* 关闭 IRQ（FIQ 保留） */
            : [cpsr]"=r"(saved_cpsr) :: "memory");

        __asm__ volatile ("DSB; ISB");    /* 清空流水线 */
        uint32_t start = read_pmccntr();
        
        /* 被测代码 开始 */
        test_function_6();
        /* 被测代码 结束 */

        __asm__ volatile ("ISB");
        uint32_t end = read_pmccntr();

        /* 恢复中断 */
        __asm__ volatile (
            "MSR CPSR_c, %[cpsr]"
            :: [cpsr]"r"(saved_cpsr)
            : "memory"
        );

        /* 处理 32-bit 回绕 */
        uint32_t delta = (end >= start)
                         ? (end - start)
                         : (0xFFFFFFFFu - start + 1u + end);

        sum_cycles += delta;
        print2("run %d cycles = %u\n", i, delta);
    }

    /* 计算并打印平均值 */
    uint32_t avg = (uint32_t)(sum_cycles / TEST_RUNS);
    print2("[INFO] average cycles over %d runs = %u\n", TEST_RUNS, avg);
}

/* ----------  SP804 初始化与读取 ---------- */
static void sp804_init(void)
{
    /* 关闭定时器 */
    WRITE_32(TIMER_CTRL, 0);

    /* 装载最大值：0xFFFF_FFFF */
    WRITE_32(TIMER_LOAD, 0xFFFFFFFFU);

    /* 使能 32-bit Free-running 模式（不产生中断） */
    WRITE_32(TIMER_CTRL, (1 << 7) | (1 << 2) | (1 << 1));
}

static inline uint32_t timer_read(void)
{
    /* 读取 VALUE 计数器向下递减 */
    return READ_32(TIMER_VALUE);
}

void through_sp804(void) {
    sp804_init();

    uint64_t sum_ticks = 0;

    for (int i = 0; i < TEST_RUNS; ++i) {

        /* 关闭 IRQ，确保测试窗口不被抢占 */
        uint32_t saved_cpsr;
        __asm__ volatile(
            "MRS %[cpsr], CPSR\n\t"
            "CPSID i"
            : [cpsr] "=r"(saved_cpsr)::"memory");

        __asm__ volatile("DSB; ISB");

        uint32_t start = timer_read();   /* ========== 读起始 ========= */

        /* ----------- 被测代码开始 ----------- */
        test_function_5();
        /* ----------- 被测代码结束 ----------- */

        uint32_t end = timer_read();     /* ========== 读结束 ========= */

        /* 恢复中断 */
        __asm__ volatile(
            "MSR CPSR_c, %[cpsr]"
            ::[cpsr] "r"(saved_cpsr)
            : "memory");

        /* Δticks = start - end；计数器向下递减，若回绕自动在无符号加法中处理 */
        uint32_t delta = start - end;
        sum_ticks += delta;

        print2("run %d ticks = %u\n", i, delta);
    }

    uint32_t avg = (uint32_t)(sum_ticks / TEST_RUNS);
    print2("[INFO] average ticks over %d runs = %u\n", TEST_RUNS, avg);
}

int main(void)
{
    /* GIC 初始化 */
    gic_init();
    
    // through_sp804();
    through_pmccntr();


    return 0;
}
