/* startup.s - 完整异常处理与中断使能 */
    .syntax unified
    .cpu cortex-a9

    .global _start
    .section .vectors, "ax", %progbits
    .align 5 @ 对齐到32字节，确保向量表正确

vectors:
    b       reset_handler               @ Reset 异常入口
    b       reset_handler               @ 未定义指令异常
    b       reset_handler               @ 软件中断异常
    b       reset_handler               @ 预取中止异常
    b       reset_handler               @ 数据中止异常
    b       reset_handler               @ 保留异常
    b       irq_handler                 @ IRQ 中断异常
    b       reset_handler               @ FIQ 中断异常

    .section .text
    .global Reset_Handler
    .extern main, __exception_vector_start
    .align 4

reset_handler:
    /* CPSR：处理器运行模式设置与中断禁用 */ 
    mrs     r0, cpsr            @ 读取 CPSR 至 R0
    bic     r0, r0, #0x1f       @ 清零 Mode 位
    orr     r0, r0, #0x13       @ 设置为 SVC 模式
    orr     r0, r0, #0xc0       @ 禁用 FIQ 与 IRQ
    msr     cpsr, r0            @ 从 R0 写回 CPSR

    /* CP15 系统控制寄存器中 V 标志置 0 */
    mrc     p15, 0, r0, c1, c0, 0
    bic     r0, r0, #(1 << 13)
    mcr     p15, 0, r0, c1, c0, 0

    /* 将 VBAR 寄存器设置为异常向量表所在地址（0x10000000） */
    ldr     r0, =__exception_vector_start
    mcr     p15, 0, r0, c12, c0, 0

    ldr sp, =stack_top     @ 初始化栈
    bl main                @ 跳转C代码
    b .                    @ 停机

    .global enable_irq
    .extern handle_irq
enable_irq:
    mrs r0, cpsr
    bic r0, #0x80          @ I位=0使能IRQ
    msr cpsr_c, r0
    bx lr

@ IRQ处理入口
irq_handler:
    sub lr, lr, #4         @ 修正返回地址
    srsdb sp!, #0x13       @ 保存SPSR和LR到SVC栈
    cpsid i, #0x13         @ 切换到SVC模式
    push {r0-r12, lr}      @ 保存寄存器
    bl handle_irq          @ 调用C处理函数
    pop {r0-r12, lr}
    rfeia sp!              @ 恢复状态