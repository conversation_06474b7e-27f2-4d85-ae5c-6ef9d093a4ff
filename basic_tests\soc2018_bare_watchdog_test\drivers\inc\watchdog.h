/* ***************************************************************************
 * 适用于A9内核的看门狗测试用例
 * ****************************************************************************
 */

#include "BSPWdt.h"

/* 测试相关常量定义 */
#define WDT_TEST_LOOP_COUNT       5     /* 喂狗测试循环次数 */

// Watchdog Timer 寄存器定义
#define WDT_BASE                    (BSP_AMBA_WDT_BASEADDR + 0x20)
#define WDT_LOAD                    (WDT_BASE + 0x00)
#define WDT_COUNTER                 (WDT_BASE + 0x04)
#define WDT_CTRL                    (WDT_BASE + 0x08)
#define WDT_ISR                     (WDT_BASE + 0x0C)
#define WDT_RESET                   (WDT_BASE + 0x10)
#define WDT_DISABLE                 (WDT_BASE + 0x14)

/* 函数声明 */
void test_mode_switch(void);
void test_watchdog_timeout(void);
void test_feed_watchdog(void);
void test_not_feed_watchdog(void);
void register_test(void);
void watchdog_test(void);