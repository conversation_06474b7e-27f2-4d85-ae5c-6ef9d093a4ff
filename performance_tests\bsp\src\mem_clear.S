//--------------------------------------------------------------------
// Function name: mem_clear
// input parameters:
//   r0: start_addr
//   r1: end_addr
// Function: clear region from start_addr to end addr
//--------------------------------------------------------------------

//void mem_clear(u32 start_addr, u32 end_addr);

.globl mem_clear

mem_clear:
	push  {r2-r9}

	mov    r2, #0
	mov    r3, #0
	mov    r4, #0
	mov    r5, #0
	mov    r6, #0
	mov    r7, #0
	mov    r8, #0
	mov    r9, #0

1:	
	stmia  r0!, {r2-r9}
	cmp    r0, r1
	bmi    1b

	pop  {r2-r9}
	bx   lr
