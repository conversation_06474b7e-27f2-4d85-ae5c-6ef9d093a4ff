#ifndef GPIO_H
#define GPIO_H

#include <stdlib.h>
#include <stdint.h>
#include <float.h>

#define GPIO_BASE_ADDR 0xA00D0000

#define GPIO_PORTA_DR      (GPIO_BASE_ADDR + 0x00) /*端口A数据寄存器*/
#define GPIO_PORTA_DDR     (GPIO_BASE_ADDR + 0x04) /*方向寄存器*/
#define GPIO_PORTA_CTL     (GPIO_BASE_ADDR + 0x08) /*端口A数据源寄存器*/
#define GPIO_INTEN         (GPIO_BASE_ADDR + 0x30) /*中断使能寄存器*/
#define GPIO_INTMASK       (GPIO_BASE_ADDR + 0x34) /*中断屏蔽寄存器*/
#define GPIO_INTTYPE_LEVEL (GPIO_BASE_ADDR + 0x38) /*中断极性寄存器*/
#define GPIO_INT_POLARITY  (GPIO_BASE_ADDR + 0x3C) /*中断极性寄存器*/
#define GPIO_INTSTATUS     (GPIO_BASE_ADDR + 0x40) /*PortA中断状态寄存器*/
#define GPIO_INT_RAWSTATUS (GPIO_BASE_ADDR + 0x44) /*PortA RAW中断状态寄存器*/
#define GPIO_PORTA_EOI     (GPIO_BASE_ADDR + 0x4C) /*PortA清除中断寄存器*/
#define GPIO_EXT_PORTA     (GPIO_BASE_ADDR + 0x50) /*外部端口A寄存器*/
#define GPIO_LS_SYNC       (GPIO_BASE_ADDR + 0x60) /*电平敏感（Level-sensitive）同步使能寄存器*/
#define GPIO_ID_CODE       (GPIO_BASE_ADDR + 0x64) /*ID标识码寄存器*/
/* reserved  0x68 */
#define GPIO_COMP_VERSION  (GPIO_BASE_ADDR + 0x6C) /*版本信息寄存器*/


uint32_t    gpio_read(uint32_t pin);

void        gpio_write(uint32_t pin, uint32_t value);

void        gpio_set_direction(uint32_t pin, uint32_t direction);

#endif

