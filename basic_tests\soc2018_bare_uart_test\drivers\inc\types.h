#ifndef TYPES_H
#define TYPES_H

#include <stdint.h>

/* 寄存器操作宏 */
#define READ_8(addr)                (*(volatile uint8_t*)(addr))
#define READ_16(addr)               (*(volatile uint16_t*)(addr))
#define READ_32(addr)               (*(volatile uint32_t*)(addr))
#define WRITE_8(addr, value)        (*(volatile uint8_t*)(addr) = (uint8_t)(value))
#define WRITE_16(addr, value)       (*(volatile uint16_t*)(addr) = (uint16_t)(value))
#define WRITE_32(addr, value)       (*(volatile uint32_t*)(addr) = (value))

#endif /* TYPES_H */
