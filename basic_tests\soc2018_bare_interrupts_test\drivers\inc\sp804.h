/*
 * SP804 驱动头文件
 */

#ifndef SP804_H
#define SP804_H

/* SP804 基址 */
#define TIMER_BASE          0x2A000000
#define TIMER_BASE_2        0x2B000000

/* SP804 寄存器 */
#define TIMER_LOAD      (TIMER_BASE + 0x00)
#define TIMER_VALUE     (TIMER_BASE + 0x04)
#define TIMER_CTRL      (TIMER_BASE + 0x08)
#define TIMER_INTCLR    (TIMER_BASE + 0x0C)

#define TIMER_LOAD_2      (TIMER_BASE_2 + 0x00)
#define TIMER_VALUE_2     (TIMER_BASE_2 + 0x04)
#define TIMER_CTRL_2      (TIMER_BASE_2 + 0x08)
#define TIMER_INTCLR_2    (TIMER_BASE_2 + 0x0C)

#endif /* SP804_H */
