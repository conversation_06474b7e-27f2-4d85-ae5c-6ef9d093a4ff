/*
 * 串口打印输出功能头文件
 */

#ifndef BSP_PRINT_H
#define BSP_PRINT_H

#include <stdarg.h>
#include <stdlib.h>
#include <stdint.h>
#include <float.h>

#include "types.h"
#include "uart.h"

#define NUM_FACTION	8	/*采用十进制输出浮点数，小数点后保留8位，可调*/
#define __FLOAT__			/*定义该宏__FLOAT__，可打印浮点数*/
#define __DOUBLE__			/*定义该宏__DOUBLE__，可打印64位整型数*/

void printn(U32 n,U32 b);	/*打印相应进制的数字*/

static const U8 charlist[16] = {	'0','1','2','3','4','5','6','7',
    '8','9','a','b','c','d','e','f' };	/*字符串查询数组*/

void printn_long(U64 n,U64 b);	/*打印相应进制的数字*/

void printm_x_10(U64 nx,U32 kp, U32 shw);/*以十进制形式打印小数部分*/

void print_f32(U32 tn_32, U8 c_8);/*打印单精度浮点数*/

void print_f64(U64 tm_64, U8 c_8);/*打印双精度浮点数*/

void print2(char fmt[],...);/*串口数据输出*/

#endif // BSP_PRINT_H
