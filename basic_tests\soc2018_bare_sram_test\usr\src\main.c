/*
 * 适用于 soc2018 的 SRAM 控制器及 EDAC 功能测试程序
 */

#include "types.h"
#include "gic.h"        /* 中断控制功能 */
#include "bsp_print.h"  /* 串口输出功能 */
#include "sram.h"
// #include "xxx.h"  // 引入外设驱动头文件

static inline void disable_alignment_check(void) {
    uint32_t sctlr;
    __asm__ volatile(
        "mrc p15, 0, %0, c1, c0, 0\n"   // 读 SCTLR
        "bic %0, %0, #0x2\n"           // 清 bit1 (A)
        "mcr p15, 0, %0, c1, c0, 0\n"   // 写回 SCTLR
        : "=&r"(sctlr)
        :
        : "memory"
    );
}

extern int irq_count;

void read_bypass_test(void) {
    /**
     * EDAC 使能/禁用测试
     */
    int val;
    volatile unsigned int *ptr = (volatile unsigned int *)0x40000000;
    for (int i = 0; i < 100; i++) {
        *ptr = 0xFFFFFFFF;
        val = *ptr++;
    }

    // WRITE_32(SRAM_MEM_CONFIG, 0x0);  /* 禁用 SRAM EDAC */

    // ptr = (volatile unsigned int *)0x40000000;
    // for (int i = 0; i < 100; i++) {
    //     *ptr = 0xFFFFFFFF;
    //     val = *ptr++;
    // }

    // WRITE_32(SRAM_MEM_CONFIG, 0x1);  /* 使能 SRAM EDAC */
    
    // ptr = (volatile unsigned int *)0x40000000;
    // for (int i = 0; i < 100; i++) {
    //     *ptr = 0xFFFFFFFF;
    //     val = *ptr++;
    // }

    /**
     * EDAC 读旁路测试
     */
    /* 先将待读旁路测试的地址空间写上数据 */
    ptr = (volatile unsigned int *)0x4000A000;
    *ptr++ = 0x9A9A9A9A;
    *ptr = 0x11111111;

    /* 读 EDAC 读旁路数据低位/高位寄存器 */
    int low_data = READ_32(SRAM_EDAC_READBYPASS_DATA_LOW);   
    int high_data = READ_32(SRAM_EDAC_READBYPASS_DATA_HIGH); 

    // GDB 读 low_data
    // GDB 读 high_data

    int cfg = 0;
    cfg = READ_32(SRAM_EDAC_READ_BYPASS_ADDR); /* 只写，应该读为0 */
    
    // GDB 读 cfg 为 0
    
    /*
     * 使能读旁路，首次触发  
     */ 
    /* 设置读旁路寄存器为 0x4000A000 */
    cfg = 0x4000A000;
    WRITE_32(SRAM_EDAC_READ_BYPASS_ADDR, cfg);
    WRITE_32(SRAM_MEM_CONFIG, 0b0011);  /* 使能读旁路 */

    /* 访问 0x4000A000 触发读旁路 */
    ptr = (volatile unsigned int *)0x4000A000;
    val = *ptr;

    /* 读 EDAC 读旁路数据低位/高位寄存器 */
    low_data = READ_32(SRAM_EDAC_READBYPASS_DATA_LOW);   
    high_data = READ_32(SRAM_EDAC_READBYPASS_DATA_HIGH); 

    // GDB 读 low_data cc 4 组 0b00001100
    // GDB 读 high_data  为 4 组 0b00011011

    /*
     * 修改读旁路地址，原地址不触发，新地址触发  
     */ 
    /* 修改读旁路寄存器为 0x40000000 */
    cfg = 0x40000000;
    WRITE_32(SRAM_EDAC_READ_BYPASS_ADDR, cfg);

    /* 此时访问 0x4000A000 不触发读旁路 */
    ptr = (volatile unsigned int *)0x4000A000;
    val = *ptr;

    /* 此时访问 0x40000000 触发读旁路 */
    ptr = (volatile unsigned int *)0x40000000;
    val = *ptr;

    /* 读 EDAC 读旁路数据低位/高位寄存器 */
    low_data = READ_32(SRAM_EDAC_READBYPASS_DATA_LOW);   
    high_data = READ_32(SRAM_EDAC_READBYPASS_DATA_HIGH); 

    // GDB 读 low_data  为 4 组 0b11000
    // GDB 读 high_data  为 4 组 0b11000

    /*
     * 清零读旁路使能位，不再触发读旁路  
     */ 
    WRITE_32(SRAM_MEM_CONFIG, 0b0001);

    for (int i = 0; i < 100; i++) {
        *ptr = 0xFFFFFFFF;
        val = *ptr++;
    }
}

void write_bypass_test(void) {
    /**
     * EDAC 写旁路测试
     */
    /* 先将待写旁路测试的地址空间写上数据 */
    volatile unsigned int *ptr = (volatile unsigned int *)0x4000A000;
    *ptr++ = 0x9A9A9A9A;
    *ptr = 0x9A9A9A9A;

    /*
     * 使能写旁路  
     */ 
    uint32_t cfg = 0x4000A000;
    WRITE_32(SRAM_EDAC_WRITE_BYPASS_ADDR, cfg); /* 设置写旁路地址 */
    WRITE_32(SRAM_EDAC_BYPASS_DATAIN, 0x1F1F1F1F);   /* 设置注入数据 */
    WRITE_32(SRAM_MEM_CONFIG, 0b0101);  /* 使能写旁路 */

    /* 写数据触发写旁路 */
    ptr = (volatile unsigned int *)0x4000A000;
    *ptr = 0;

    // 此时 0x4000A000 对应的校验空间的 20bit 为全 1，之后 20bit 为 4 组 0b01100

    /* 配置读旁路并使能，以查看校验数据是否为写旁路注入的 */
    WRITE_32(SRAM_EDAC_READ_BYPASS_ADDR, cfg);
    WRITE_32(SRAM_MEM_CONFIG, 0b0111);  /* 使能读旁路 */

    ptr = (volatile unsigned int *)0x4000A000;
    int val = *ptr; /* 触发读旁路 */

    /* 读 EDAC 读旁路数据低位/高位寄存器 */
    int low_data = READ_32(SRAM_EDAC_READBYPASS_DATA_LOW);
    int high_data = READ_32(SRAM_EDAC_READBYPASS_DATA_HIGH);

    // GDB 读 low_data 应为 0b11111
    // GDB 读 high_data 应为 0b01100

    /*
     * 清零读旁路使能位，不再触发写旁路  
     */ 
    WRITE_32(SRAM_MEM_CONFIG, 0b0001);

    ptr = (volatile unsigned int *)0x4000A000;
    *ptr = 0xFFFFFFFF;
}

void read_bypass_analysis(void) {
    /* 
     * 读写旁路地址特性分析
     */
    print2("\n==============================\n");

    volatile unsigned long long temp;
    volatile unsigned int *mec;
    volatile unsigned int bypass, ErrAddr1;
    volatile unsigned int cb = 0;

    mec = (V_U32 *)0x40200000;
    mec[0x00/4] = 0x1;			// edac  开 

    ErrAddr1 = 0x4000FFF0;
    volatile unsigned int ErrAddr2 = 0x4000FFF8;

    volatile unsigned int bypass_addr = 0x4000FFF8;
    print2("Write bypass address: 0x%x\n", bypass_addr);
    mec[0x48/4] = bypass_addr;		// 设置读旁路地址
    mec[0x00/4] |= 0x2;				// 设置EDAC 读旁路使能

    /* 数据初始化 */
    *(volatile unsigned long long *)(ErrAddr1) = 0xAAAAAAAABBBBBBBB;
    *(volatile unsigned long long *)(ErrAddr2) = 0xCCCCCCCCDDDDDDDD;

    /* 第一次 EDAC 校验码读取 */
    temp = *(volatile unsigned long long *)(ErrAddr1 + 4);
    cb = mec[0x2c/4];
    print2("High 32 bits: 0x%x\n", cb);
    cb = mec[0x28/4];
    print2("Low 32 bits: 0x%x\n", cb);

    /* ErrAddr1 写入新数据后 EDAC */
    // *(V_U64 *)ErrAddr1 = 0x12345678ABCDEF00;
    // *(V_U64 *)ErrAddr2 = 0xAAAAAAAABBBBBBBB;
    // print2("After Write\n");
    // temp = *(V_U64 *)(ErrAddr1 + 4);
    // cb = mec[0x2c/4];
    // print2("High 32 bits: 0x%x\n", cb);
    // cb = mec[0x28/4];
    // print2("Low 32 bits: 0x%x\n", cb);

    mec[0x00/4] &= 0xd;			// 设置EDAC 读旁路禁止

    print2("==============================\n");
}

int main(void)
{
    /* GIC 初始化 */
    gic_init();
    
    /* 业务代码开始 */
    // read_bypass_test();
    // write_bypass_test();
    read_bypass_analysis();

    /* 业务代码结束 */

    return 0;
}
