/*
 * 用于测试 QEMU 模拟 ARM 架构虚拟机时 TLB 慢路径的访存宽度
 */

#include "types.h"
#include "gic.h"        /* 中断控制功能 */
#include "bsp_print.h"  /* 串口输出功能 */
#include <arm_neon.h>

extern int irq_count;

#define ENABLE_NEON      1     /* 1=使用 NEON 128-bit，0=跳过   */
#define ENABLE_ASM_64BIT 1     /* 1=用内联 asm 测 64-bit LDRD  */

/* ------------- 共享缓冲区 ----------------------------------- */
#define BUF_SIZE 64U                      /* 必须 16 的倍数 */
static uint8_t g_test_buf[BUF_SIZE] __attribute__((aligned(64)));

/* 验证缓冲区是否全填充为 fill；返回 0=OK，非 0=第一个错误下标+1 */
static int verify_pattern(uint8_t fill)
{
    for (unsigned i = 0; i < BUF_SIZE; ++i)
        if (g_test_buf[i] != fill) return (int)i + 1;
    return 0;
}

/* ---------- 8-bit 访问（最小宽度） --------------------------- */
int test_byte_access(void)
{
    volatile uint8_t *p = g_test_buf;

    /* 写 0xAA */
    for (unsigned i = 0; i < BUF_SIZE; ++i) p[i] = 0xAA;

    /* 读回校验 */
    return verify_pattern(0xAA);
}

/* ---------- 16-bit 访问 ------------------------------------- */
int test_halfword_access(void)
{
    volatile uint16_t *p = (uint16_t *)g_test_buf;

    for (unsigned i = 0; i < BUF_SIZE / 2U; ++i) p[i] = 0xBBBB;

    return verify_pattern(0xBB);
}

/* ---------- 32-bit 访问 ------------------------------------- */
int test_word_access(void)
{
    volatile uint32_t *p = (uint32_t *)g_test_buf;

    for (unsigned i = 0; i < BUF_SIZE / 4U; ++i) p[i] = 0xCCCCCCCCUL;

    return verify_pattern(0xCC);
}

/* ---------- 64-bit 访问 ------------------------------------- */
__attribute__((noinline))
int test_doubleword_access(void)
{
    uint32_t *p    = (uint32_t *)g_test_buf;
    uint32_t loops = BUF_SIZE / 8U;       /* 8 字节一次 */

    uint32_t hi = 0xDDDDDDDDU;
    uint32_t lo = 0xDDDDDDDDU;

    asm volatile(
        "1:                                       \n"
        "  STRD   %[lo], %[hi], [%[ptr]], #8      \n" /* 64-bit store */
        "  SUBS   %[loops], %[loops], #1          \n"
        "  BNE    1b                              \n"
        : [ptr] "+r"(p), [loops] "+r"(loops)
        : [hi] "r"(hi), [lo] "r"(lo)
        : "memory"
    );

    return verify_pattern(0xDD);
}

/* ---------- 128-bit 访问（NEON：最大指令宽度） -------------- */
int test_neon_add_max(void)
{
    volatile uint8_t * const src1 = (uint8_t *)0x40000000;
    volatile uint8_t * const src2 = (uint8_t *)0x40000A00;
    volatile uint8_t * const dst  = (uint8_t *)0x40000B00;

    /* ---------- 1. 初始化：16 字节全 0x11 / 0x22 ---------------- */
    for (int i = 0; i < 16; ++i) {
        src1[i] = 0x11;
        src2[i] = 0x22;
    }

    /* ---------- 2. 加载向量 ----------------------------------- */
    uint8x16_t v1 = vld1q_u8((const uint8_t *)src1);   /* 128-bit load */
    uint8x16_t v2 = vld1q_u8((const uint8_t *)src2);

    /* ---------- 3. 向量运算 ----------------------------------- */
    uint8x16_t sum  = vaddq_u8(v1, v2);                /* sum = 0x33 */
    uint8x16_t mask = vcgtq_u8(v1, v2);                /* 全 0，因 v1<v2 */
    uint8x16_t maxv = vbslq_u8(mask, v1, v2);          /* 0x22 */
    (void)maxv;

    /* ---------- 4. 存回并校验 -------------------------------- */
    vst1q_u8((uint8_t *)dst, sum);                     /* 128-bit store */

    for (int i = 0; i < 16; ++i) {
        if (dst[i] != 0x33) {     /* 0x11 + 0x22 = 0x33 */
            return i + 1;
        }      
    }

    return 0;
}

/* NEON 复制加载（批量写入）测试 */
int test_neon_dup(void)
{
    volatile uint8_t * const src = (uint8_t *)0x40000C00;
    volatile uint8_t * const dst = (uint8_t *)0x40000C10;
    const uint8_t val = 0x44;

    /* 在 src 处写入一个字节 0x44*/
    src[0] = val;

    /* NEON 复制加载：将该字节复制到整个 uint8x16_t 向量的 16 个元素 */
    uint8x16_t vdup = vld1q_dup_u8((const uint8_t *)src);

    /* 存回 */
    vst1q_u8((uint8_t *)dst, vdup);

    /* 验证 */
    for (int i = 0; i < 16; ++i) {
        if (dst[i] != val)
            return i + 1;
    }
    return 0;
}

/* NEON lane 加载（批量写入）测试 */
int test_neon_lane(void)
{
    volatile uint8_t * const src = (uint8_t *)0x40000D00;
    volatile uint8_t * const dst = (uint8_t *)0x40000D10;
    const uint8_t new_val = 0x55;

    /* 先把 dst 全填 0xAA */
    for (int i = 0; i < 16; ++i)
        dst[i] = 0xAA;

    /* 准备全 0xAA 的向量 */
    uint8x16_t vec = vdupq_n_u8(0xAA);

    /* 写入要更新的单个字节 */
    src[0] = new_val;

    /* 替换 lane 索引为 7 的元素为 0x55 */
    uint8x16_t vl = vld1q_lane_u8((const uint8_t *)src, vec, 7);

    /* 存回 */
    vst1q_u8((uint8_t *)dst, vl);

    /* 验证 */
    for (int i = 0; i < 16; ++i) {
        uint8_t expect = (i == 7 ? new_val : 0xAA);
        if (dst[i] != expect)
            return i + 1;
    }
    return 0;
}

int main(void)
{
    /* GIC 初始化 */
    gic_init();
    
    /* 业务代码开始 */
    struct {
        const char *name;
        int (*fn)(void);
    } cases[] = {
        // {"BYTE  8-bit",  test_byte_access},
        // {"HWORD 16-bit", test_halfword_access},
        // {"WORD  32-bit", test_word_access},
        // {"DWORD 64-bit", test_doubleword_access},
        // {"NEON 128-bit", test_neon_add_max},
        {"NEON 128-bit", test_neon_dup},
        {"NEON 128-bit", test_neon_lane},
    };

    int fails = 0;
    for (unsigned i = 0; i < sizeof(cases)/sizeof(cases[0]); ++i) {
        int err = cases[i].fn();
        if (err) {
            print2("[FAIL] %s  first mismatch at +%d\n",
                   cases[i].name, err - 1);
            ++fails;
        } else {
            print2("[PASS] %s\n", cases[i].name);
        }
    }
    
    /* 业务代码结束 */

    return 0;
}
