#ifndef __SYS_SPACELIB_H__
#define __SYS_SPACELIB_H__

#include "UTILStd.h"
#include "BSPConfig.h"

#ifndef __ASMLANG__
extern U32 SYS_GetUsrTaskStatus(U32 id);
extern U32 SYS_GetLoaderResetFlag (void);
extern U32 SYS_GetLoaderResetSum(void);
extern U32 SYS_GetLoaderRebootTimes(void);
extern void SYS_ClearLoaderRebootTimes(void);
extern U32 SYS_GetLoaderTrapType(void);
//extern void SYS_ProgReloadReset(void);
extern void SYS_SetLoaderProgLoadFlag(U32 flag);
extern U32 SYS_GetLoaderProgIsLoaded(void);

#if (GFS_AB_MACHINE_SWITCH_FLAG==1)
extern U32 SYS_GetLoaderAutoSwitchCmd(void);
extern void SYS_SetLoaderAutoSwitchCmd(U32 flag);
#endif

extern void SYS_SoftReset(void);
extern void SYS_SysSoftReset(void);
extern U32 BSP_MemRefresh(U32 base, U32 offsetAddr, U32 wordSize);
extern U32 SYS_GetTaskInfo(U32 taskId, U32 infoType);
//extern void SYS_Delay(U32 dtm);
//extern U32 SYS_GetGPT(void);
//extern U32 SYS_GPTDiff(U32 timeold,U32 timenew);
extern void SYS_RTCTReset(void);
extern void SYS_IO_Write(U32 ioAddr,U32 data);
extern U32 SYS_IO_Read(U32 ioAddr);
//extern U32 SYS_IntEnable(U32 isrNo);
//extern U32 SYS_IntDisable(U32 isrNo);

extern void SYS_TimerStart(U32 timerNo, U32 delay, FUNCPTR func);
extern void SYS_TimerClear(U32 timerNo);

extern void SYS_Set_Prom_Width(U32 width);
extern void SYS_Set_Prom_EDAC(U32 option);
extern void SYS_FillNop(void);
extern void SYS_GicSeuIntEnable(void);
extern void SYS_GicSeuIntDisable(void);
#ifdef DEBUG
//�����汾��Ϣ
extern void SYS_ShowVersion(void);
#endif
#endif
#endif

