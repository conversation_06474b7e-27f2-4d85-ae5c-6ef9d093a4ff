/*
 * GIC 驱动头文件
 */

#ifndef GIC_H
#define GIC_H

#include "types.h"

/* 中断数量 */
#define INTERNAL_INT_NUM 32
#define EXTERNAL_INT_NUM 128
#define TOTAL_INT_NUM (INTERNAL_INT_NUM + EXTERNAL_INT_NUM)

/* ISR 类型 - 暂未在运行时发挥作用 */
#define ISR_TYPE_UNUSED     0x0  /* 未使用中断类型标识 */
#define ISR_TYPE_INTERNAL   0x1  /* 内部中断类型标识 */
#define ISR_TYPE_EXTERNAL   0x2  /* 外部中断类型标识 */

/* GIC寄存器 */
#define GICD_BASE           0x3FFF1000
#define GICC_BASE           0x3FFF0100

/* GIC Distributor 寄存器 */
#define GICD_ICCDCR         (GICD_BASE + 0x000)  /* 分配器控制寄存器 */
#define GICD_ICDICTR        (GICD_BASE + 0x004)  /* 中断控制器类型寄存器 - RO GIv2 */
#define GICD_ICDIIDR        (GICD_BASE + 0x008)  /* 分配器版本信息寄存器 - RO 0x0000043B */
#define GICD_ICDISRn        (GICD_BASE + 0x080)  /* 中断安全（分组）寄存器 */
#define GICD_ICDISERn       (GICD_BASE + 0x100)  /* 中断使能寄存器 */
#define GICD_ICDICERn       (GICD_BASE + 0x180)  /* 中断挂起清除寄存器组 */
#define GICD_ICDISPRn       (GICD_BASE + 0x200)  /* 中断挂起设置寄存器组 */
#define GICD_ICDICPRn       (GICD_BASE + 0x280)  /* 中断挂起清除寄存器组 */
#define GICD_ICDIABRn       (GICD_BASE + 0x300)  /* 中断 Active 寄存器组 - RO */
#define GICD_ICDIPRn        (GICD_BASE + 0x400)  /* 中断优先级寄存器组 */
#define GICD_ICDIPTRn       (GICD_BASE + 0x800)  /* 中断目标 CPU 寄存器组 - 单核 RAZ/WI */
#define GICD_ICDICFRn       (GICD_BASE + 0xC00)  /* 中断配置寄存器组 */
#define GICD_ICDSGIR        (GICD_BASE + 0xF00)  /* SGI 触发寄存器 */

/* GIC CPU Interface 寄存器 */
#define GICC_ICCICR         (GICC_BASE + 0x00)   /* CPU 接口控制寄存器 */
#define GICC_ICCPMR         (GICC_BASE + 0x04)   /* 中断优先级屏蔽寄存器 */
#define GICC_ICCBPR         (GICC_BASE + 0x08)   /* 二进制点寄存器 (Secure) */
#define GICC_ICCIAR         (GICC_BASE + 0x0C)   /* 中断响应寄存器 - RO */
#define GICC_ICCEOIR        (GICC_BASE + 0x10)   /* 中断结束寄存器 - WO */
#define GICC_ICCRPR         (GICC_BASE + 0x14)   /* 响应中断优先级寄存器 - RO */
#define GICC_ICCHPIR        (GICC_BASE + 0x18)   /* 最高挂起中断寄存器 - RO */
#define GICC_ICCABPR        (GICC_BASE + 0x1C)   /* 二进制点寄存器 (Non-Secure) */

/* ISR Map */
typedef struct isr_map {
    uint32_t Type;              /* 中断类型 */
    uintptr_t Handler;          /* 中断处理函数的地址 */
    uint32_t Arguments[2];      /* 中断处理函数的参数 */
} ISR_MAP;

/* GIC 初始化 */
void gic_init();

/* IRQ 异常服务例程 */
void gic_handle_irq();

/*
 * 外设相关定义
 */
/* 外设中断号 - 各外设的中断号码都应定义在此处 */
#define TIMER_IRQ_ID    95 /* SP804 使用 SoC2018 保留未用的中断ID */
#define UART0_IRQ_ID    61 /* uart0 中断ID */
#define UART1_IRQ_ID    62 /* uart1 中断ID */
#define GPIO0_IRQ_ID    34 /* 16路GPIO外部中断的0号引脚*/
#define GPIO1_IRQ_ID    35 /* 16路GPIO外部中断的1号引脚*/
#define EMIF_IRQ_SE_ID  81  /* EMIF 单错中断ID */
#define EMIF_IRQ_DE_ID  86  /* EMIF 多错中断ID */
#define EMIF_WR2OR_INT  82  /* EMIF ROM 写访问错 */
#define EMIF_IRQ_IO_ID  83  /* EMIF IO 中断ID */
// #define XXX_IRQ_ID

/* ISRs - 各外设的中断服务例程都应声明在此处 */
void sp804_isr();
void uart0_isr();
void gpio_isr();
void emif_se_isr();
void emif_de_isr();
void emif_io_isr();
// void xxx_isr();

#endif /* GIC_H */
