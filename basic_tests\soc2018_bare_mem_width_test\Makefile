# 工具链配置(请自行修改GCC_HOME)
GCC_HOME = 
CC = arm-none-eabi-gcc
AS = arm-none-eabi-gcc
LD = arm-none-eabi-gcc
OBJDUMP = arm-none-eabi-objdump
OBJCOPY = arm-none-eabi-objcopy
READELF = arm-none-eabi-readelf

# 编译器选项
CFLAGS = -c -g -O0 -mfloat-abi=hard -mfpu=neon-vfpv3 -mcpu=cortex-a9
ASFLAGS = -c -g -mfloat-abi=hard -mfpu=neon-vfpv3 -mcpu=cortex-a9
LDFLAGS = -g -nostartfiles
LDFLAGS += -lm

# 标准库链接信息
GCC_LIB += -L $(GCC_HOME)/lib/gcc/arm-none-eabi/9.2.1/arm/v5te/hard
GCC_LIB += -L $(GCC_HOME)/arm-none-eabi/lib/arm/v5te/hard
GCC_LIB += -L $(GCC_HOME)/arm-none-eabi/lib
GCC_LIB += -L .

# 链接规则定义
LINK = $(LDFLAGS) -T link.ld $(GCC_LIB)

# 头文件目录
INCDIRS := drivers/inc

# 源文件目录
SRCDIRS := usr/src
SRCDIRS += drivers/src

# 相对路径文件名提取
ASFILES := $(foreach dir, $(SRCDIRS), $(wildcard $(dir)/*.S))
CFILES	:= $(foreach dir, $(SRCDIRS), $(wildcard $(dir)/*.c))

# 纯文件名提取
ASFILENDIR := $(notdir  $(ASFILES))
CFILENDIR := $(notdir  $(CFILES))

# 头文件
INCLUDE	:=	$(patsubst %, -I %, $(INCDIRS))

# 待构建文件
OBJDIR := build
ASOBJS := $(patsubst %.S, $(OBJDIR)/%.o, $(ASFILENDIR))
COBJS := $(patsubst %.c, $(OBJDIR)/%.o, $(CFILENDIR))
OBJS := $(ASOBJS) $(COBJS)

# VPATH 设置
VPATH := $(SRCDIRS)

# 目标规则
TARGET = app.elf
all: $(TARGET)
	
# 链接目标文件
$(TARGET): $(OBJS)
	$(LD) $^ -o $@ $(LINK)

# 创建build目录
$(OBJDIR)/%.o: %.S | $(OBJDIR)
	$(AS) $(ASFLAGS) $(INCLUDE) -o $@ $<

$(OBJDIR)/%.o: %.c | $(OBJDIR)
	$(CC) $(CFLAGS) $(INCLUDE) -o $@ $<

# 创建build目录的规则
$(OBJDIR):
	mkdir $(OBJDIR)

# 清理目标文件
clean:
	@if exist $(OBJDIR) rmdir /S /Q $(OBJDIR)
	@if exist $(TARGET) del $(TARGET)
	@echo Cleaning complete: $(OBJDIR) dir and $(TARGET) removed.

show:
	@echo "=========================================="
	@echo "ASFILES = $(ASFILES)"
	@echo "CFILES = $(CFILES)"
	@echo "=========================================="
	@echo "OBJS = $(OBJS)"
	@echo "=========================================="

# 辅助规则
.PHONY: all clean show
