/*
 * UART 驱动头文件
 */

 #ifndef UART_H
 #define UART_H

#include <stdlib.h>
#include <stdint.h>
#include <float.h>

#define SOC2018_E_DEV_UART0 0xA0000000
#define SOC2018_E_DEV_UART1 0xA0000100

#define SOC2018_UART0_CLK 400000000 //时钟频率400MHz

// UART0寄存器偏移地址定义
#define UART_RX (SOC2018_E_DEV_UART0 + 0x00) // 接收寄存器
#define UART_TX (SOC2018_E_DEV_UART0 + 0x00) // 发送寄存器
#define UART_DLL (SOC2018_E_DEV_UART0 + 0x00) // 分频锁存器（低8位）
#define UART_DLH (SOC2018_E_DEV_UART0 + 0x01) // 分频锁存器（高8位）
#define UART_IER (SOC2018_E_DEV_UART0 + 0x04) // 中断使能寄存器
#define UART_IIR (SOC2018_E_DEV_UART0 + 0x08) // 中断标识寄存器
#define UART_FCR (SOC2018_E_DEV_UART0 + 0x08) // FIFO控制寄存器
#define UART_LCR (SOC2018_E_DEV_UART0 + 0x0C) // 线路控制寄存器
#define UART_MCR (SOC2018_E_DEV_UART0 + 0x10) // 调制解调器控制寄存器
#define UART_LSR (SOC2018_E_DEV_UART0 + 0x14) // 线路状态寄存器
#define UART_MSR (SOC2018_E_DEV_UART0 + 0x18) // 调制解调器状态寄存器
#define UART_SCR (SOC2018_E_DEV_UART0 + 0x1C) // 临时寄存器
#define UART_MAX (SOC2018_E_DEV_UART0 + 0x20) // 串口寄存器偏移最大值

// UART1 的寄存器地址偏移
#define UART1_RX (SOC2018_E_DEV_UART1 + 0x00) // 接收寄存器
#define UART1_TX (SOC2018_E_DEV_UART1 + 0x00) // 发送寄存器
#define UART1_DLL (SOC2018_E_DEV_UART1 + 0x00) // 分频锁存器（低8位）
#define UART1_DLH (SOC2018_E_DEV_UART1 + 0x01) // 分频锁存器（高8位）
#define UART1_IER (SOC2018_E_DEV_UART1 + 0x04) // 中断使能寄存器
#define UART1_IIR (SOC2018_E_DEV_UART1 + 0x08) // 中断标识寄存器
#define UART1_FCR (SOC2018_E_DEV_UART1 + 0x08) // FIFO控制寄存器
#define UART1_LCR (SOC2018_E_DEV_UART1 + 0x0C) // 线路控制寄存器
#define UART1_MCR (SOC2018_E_DEV_UART1 + 0x10) // 调制解调器控制寄存器
#define UART1_LSR (SOC2018_E_DEV_UART1 + 0x14) // 线路状态寄存器
#define UART1_MSR (SOC2018_E_DEV_UART1 + 0x18) // 调制解调器状态寄存器
#define UART1_SCR (SOC2018_E_DEV_UART1 + 0x1C) // 临时寄存器
#define UART1_MAX (SOC2018_E_DEV_UART1 + 0x20) // 串口寄存器偏移最大值

/* IER寄存器相关宏定义 */
#define UART_IER_PTIME  (1 << 7)  // THRE 中断使能寄存器
#define UART_IER_EDSSI  (1 << 3)  // Modem 状态中断使能寄存器
#define UART_IER_ELSI   (1 << 2)  // 接收线状态中断使能寄存器
#define UART_IER_ETBEI  (1 << 1)  // 发送缓存寄存器为空中断使能寄存器
#define UART_IER_ERBFI  (1 << 0)  // 接收数据可用中断使能寄存器

/* IIR寄存器相关宏定义 */
#define UART_IIR_FIFO_ENABLED (3 << 6)  // FIFO 使能寄存器
#define UART_IIR_INT_ID_MASK  0x0F      // 中断 ID 掩码
#define UART_IIR_INT_MODEM    0x00      // Modem 状态
#define UART_IIR_NO_INT       0x01      // 无中断挂起
#define UART_IIR_INT_THR      0x02      // THR 空
#define UART_IIR_INT_RX_AVAIL 0x04      // 接收数据可用
#define UART_IIR_INT_RX_LINE  0x06      // 接收线状态
#define UART_IIR_INT_BUSY     0x07      // 忙
#define UART_IIR_INT_TIMEOUT  0x0C      // 字符超时

/* FCR寄存器相关宏定义 */
#define UART_FCR_RT_MASK  (3 << 6)  // RCVR 触发掩码
#define UART_FCR_RT_1     (0 << 6)  // FIFO 中有 1 字节
#define UART_FCR_RT_1_4   (1 << 6)  // FIFO 1/4 满
#define UART_FCR_RT_1_2   (2 << 6)  // FIFO 1/2 满
#define UART_FCR_RT_2_LESS (3 << 6) // FIFO 比满少 2 字节
#define UART_FCR_TET_MASK (3 << 4)  // TX 空触发掩码
#define UART_FCR_TET_EMPTY (0 << 4) // FIFO 空
#define UART_FCR_TET_2     (1 << 4) // FIFO 中有 2 字节
#define UART_FCR_TET_1_4   (2 << 4) // FIFO 1/4 满
#define UART_FCR_TET_1_2   (3 << 4) // FIFO 1/2 满
#define UART_FCR_XFIFOR    (1 << 2) // XMIT FIFO 复位
#define UART_FCR_RFIFOR    (1 << 1) // RCVR FIFO 复位
#define UART_FCR_FIFOE     (1 << 0) // FIFO 使能

/* LCR寄存器相关宏定义 */
#define UART_LCR_DLAB  (1 << 7)  // 分频系数锁存位
#define UART_LCR_BC    (1 << 6)  // Break 控制位
#define UART_LCR_PAR   (1 << 5)  // 校验位
#define UART_LCR_EPS   (1 << 4)  // 校验极性
#define UART_LCR_PEN   (1 << 3)  // 校验使能
#define UART_LCR_STOP  (1 << 2)  // 停止位
#define UART_LCR_DLS_MASK 0x03   // 数据长度选择掩码
#define UART_LCR_DLS_5BIT 0x00   // 5bit 数据长度
#define UART_LCR_DLS_6BIT 0x01   // 6bit 数据长度
#define UART_LCR_DLS_7BIT 0x02   // 7bit 数据长度
#define UART_LCR_DLS_8BIT 0x03   // 8bit 数据长度

/* LSR寄存器相关宏定义 */
#define UART_LSR_RFE  (1 << 7)  // 接收 FIFO 错误位
#define UART_LSR_TEMT (1 << 6)  // 发送空
#define UART_LSR_THRE (1 << 5)  // 发送保持寄存器为空
#define UART_LSR_BI   (1 << 4)  // Break 中断位
#define UART_LSR_FE   (1 << 3)  // 帧错误位
#define UART_LSR_PE   (1 << 2)  // 校验极性错误位
#define UART_LSR_OE   (1 << 1)  // 溢出错误位
#define UART_LSR_DR   (1 << 0)  // 数据准备好位

typedef unsigned int			U32;
typedef volatile U32			V_U32;

void uart_putc(int c);

void uart_puts(const char *str);

void uart1_putc(int c);

void uart1_puts(const char *str);

char uart_getc(void);

void uart_gets(char *buffer, int size);

void UART0_Init(uint32_t baudrate);

void UART0_SetBaudRate(uint32_t baudrate);

void UART0_EnableFIFO(void);

void UART0_DisableFIFO(void);

void UART0_ResetFIFO(void);

void UART0_SetDataLength(uint8_t length);

void UART0_EnableInterrupt(uint32_t interrupt);

void UART0_DisableInterrupt(uint32_t interrupt);

#endif // BSP_UART_H
