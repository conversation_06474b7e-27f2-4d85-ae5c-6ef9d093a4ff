# Changelog

<!-- ## [vx.x] - 202x-xx-xx

- 这是一个模板，可选中片段后，使用“Ctrl+/”快捷键解注释

### Added

- **无**

### Changed

- **无**

### Removed

- **无** -->

## [v0.17] - 2025-08-25

- 添加了 soc2018_bare_rom_and_undefined_instruction_test 测试文件夹，用于提供 SoC2018 模拟器的 ROM 及 SpaceOS 运行中的未定义指令问题相关的测试程序。
- 修改了 performance_tests 测试文件夹中的代码，以测试 SoC2018 环境下 SpaceOS 运行中的未定义指令相关问题。

### Added

- **basic_tests/soc2018_bare_rom_and_undefined_instruction_test/\***
    - 用于提供 SoC2018 模拟器的 ROM 及 SpaceOS 运行中的未定义指令问题相关的测试程序。

### Changed

- **performance_tests/main.c**
    - 添加了用于测试 SoC2018 环境下 SpaceOS 运行中的未定义指令相关问题的函数。

### Removed

- **无**

## [v0.16] - 2025-07-15

- 添加了 performance_tests 测试文件夹，用于提供兼容 SoC2018 以及 SoC2018 模拟器的测试程序。
- 添加了 soc2018_bare_edac_performance_test 测试文件夹，用于提供 SoC2018 EDAC TLB 快慢路径性能测试程序。
- 将 use_case_tests 中的 UART 驱动更新，同步 v0.15 版本的相关修改。

### Added

- **performance_tests/\***
    - 用于提供兼容 SoC2018 以及 SoC2018 模拟器的测试程序。

- **basic_tests/soc2018_bare_edac_performance_test/\***
    - 用于提供 SoC2018 EDAC TLB 快慢路径性能测试程序。

### Changed

- **use_case_tests/main.c**
    - 添加了串口初始化代码，用于兼容规范实现后的 UART 串口初始化操作。

- **use_case_tests/uart.h**
    - 对齐 v0.15 版本对 basic_tests 中 UART 驱动的修改。

- **use_case_tests/uart_driver.c**
    - 对齐 v0.15 版本对 basic_tests 中 UART 驱动的修改。

### Removed

- **无**

## [v0.15] - 2025-07-11

- 对 qemu_soc2018 仓库的串口寄存器偏移修改进行了适配，保证串口功能正常使用。

### Added

- **无**

### Changed

- **basic_tests/soc2018_xxx_test/drivers/inc/uart.h**
    - 对 UART 寄存器偏移进行修改。

- **basic_tests/soc2018_xxx_test/drivers/src/uart.c**
    - 对 UART 寄存器访问操作与驱动代码进行修改。

### Removed

- **无**

## [v0.14] - 2025-06-25

- 引入了测试用例专用测试目录 use_case_tests 和基础模块测试目录 basic_tests。
- 测试用例目录中的子目录与测试用例表中硬件模块一一对应，实现中的函数与测试用例表中测试用例一一对应。
- 编译测试用例程序时请在 use_case_tests/ 目录中，执行相应构建指令即可。

### Added

- **basic_tests/\***
    - 用于提供基础模块测试程序。

- **use_case_tests/\***
    - 用于提供测试用例专用测试程序。

### Changed

- **无**

### Removed

- **\***
    - 现基础模块测试代码均位于 basic_tests 目录下。

## [v0.13] - 2025-06-20

- 添加 CAN 相关的测试，包含 CAN 的模式测试、过滤测试、中断测试等 CAN 核心功能测试。
- 移植502驱动函数中 CAN 相关的驱动函数，能够基本适配相关定义与函数。

### Added
- **soc2018_bare_can_test/\***
    - 用于提供 CAN 相关测试程序。

- **drviers/inc/gen_h/\***
    - CAN 依赖的502驱动函数提供的系列头文件。

- **drviers/inc/DRV_xxx.h***
    - 同理，CAN 依赖的502驱动函数提供的系列头文件。

### Changed

- **drivers/source/gic.c**
    - 添加了对 CAN 中断的注册,依托的设备名称为 IOASIC , CAN和其他组件一起共享 IOASIC 的中断。

### Removed

- **无**

## [v0.12] - 2025-06-18

- 将 soc2018_bare_edac_test 测试文件夹，更名为 soc2018_bare_emif_test。
- 完善了 soc2018_bare_emif_test 测试文件夹，提供了 EMIF 的 EDAC 相关测试代码。
- 修正了 SDRAM 测试代码中的一些问题。

### Added

- **soc2018_bare_emif_test/\***
    - 用于提供 soc2018 EMIF 相关测试程序，由 soc2018_bare_edac_test 更名而来。

### Changed

- **soc2018_bare_sdram_test/usr/src/main.c**
    - 修正了 SDRAM 测试代码中的一些问题。

### Removed

- **soc2018_bare_edac_test/\***
    - 现更名为 soc2018_bare_emif_test。

## [v0.11] - 2025-06-11

- 添加了基于 soc2018_WatchDog_BSP 的 a9mpcore WatchDog 测试逻辑及文件夹。

### Added

- **soc2018_bare_watchdog_test/\***
    - 用于提供 soc2018 WatchDog 相关测试程序。

- **soc2018_bare_watchdog_test/drivers/inc/BSPWdt.h
    - 用于兼容 soc2018_WatchDog_BSP。

- **soc2018_bare_watchdog_test/drivers/src/BSPWdt.c
    - 用于兼容 soc2018_WatchDog_BSP。

- **soc2018_bare_watchdog_test/drivers/src/watchdog.c
    - 用于基于 soc2018_WatchDog_BSP 代码提供测试样例。

### Changed

- **无**

### Removed

- **无**

## [v0.10] - 2025-06-05

- 添加了 soc2018_bare_sram_test 测试文件夹，用于提供 SoC2018 SRAM、SRAM 控制器及其 EDAC 功能的测试程序。
- 添加了 soc2018_bare_sdram_test 测试文件夹，用于提供 SoC2018 SDRAM、SDRAM控制器及其 EDAC 功能的测试程序。
- 更新了 soc2018_bare_xxx 模板，现在模板的驱动文件夹中包含 SRAM 和 SDRAM 相关驱动，并且全局中断映射表也添加了相应中断信息。

### Added

- **soc2018_bare_sram_test/\***
    - 用于提供 soc2018 SRAM 相关测试程序。

- **soc2018_bare_sdram_test/\***
    - 用于提供 soc2018 SDRAM 相关测试程序。

- **soc2018_bare_xxx/\*\*/sram\***
    - - 添加了模板用户程序对 SRAM 的支持。

- **soc2018_bare_xxx/\*\*/sdram\***
    - - 添加了模板用户程序对 SDRAM 的支持。

- **soc2018_bare_xxx/\*\*/emif\***
    - - 添加了模板用户程序对 SDRAM 的支持。

### Changed

- **soc2018_bare_xxx/\*\*/gic.\***
    - 添加了模板用户程序对 SRAM 和 SDRAM 的支持。

- **soc2018_bare_xxx/drivers/inc/types.h**
    - 添加了模板用户程序对 EMIF 的支持。

### Removed

- **无**

## [v0.9] - 2025-06-03

- 完善了 soc2018_bare_edac_test 测试文件夹，提供了 EMIF 器件的用户驱动代码和 ISR 中断服务例程。

### Added

- **soc2018_bare_edac_test/drivers/inc/emif.h**
    - 用于提供 EMIF 器件驱动函数需要用到的宏定义以及函数声明。

- **soc2018_bare_edac_test/drivers/src/emif_driver.c**
    - 用于提供 EMIF 器件驱动函数具体实现。

### Changed

- **soc2018_bare_edac_test/drivers/inc/gic.h**
    - 添加了 EMIF 器件中断 ID 与 ISR 声明。

- **soc2018_bare_edac_test/drivers/inc/types.h**
    - 添加了 EMIF 器件驱动需要用到的类型声明。

- **soc2018_bare_edac_test/drivers/src/gic.c**
    - 添加了 EMIF 器件 ISR 注册代码。

- **soc2018_bare_edac_test/usr/src/main.c**
    - 添加了 EMIF 器件驱动与中断测试代码。

### Removed

- **无**

## [v0.8] - 2025-05-09
- 添加了 soc2018_bare_edac_test 测试文件夹，用于提供 SoC2018 EDAC 测试程序。
- soc2018_bare_edac_test 测试文件夹的启动文件 start.S 的注释中包含有一套 MMU 设置规则，将0x40000000-0x4FFFFFFF 和 0x90000000-0x9FFFFFFFF 的 GPA 区域交换，用于测试 GVA 和 GPA 非一一对应情况下的 EDAC 机制。
- 更新了 soc2018_bare_xxx 模板，现在模板的驱动文件夹中包含 GPIO 相关驱动，并且全局中断映射表也添加了 GPIO 中断信息。

### Added

- **soc2018_bare_edac_test/\***
    - 用于提供 soc2018 EDAC 测试程序。

### Changed

- **soc2018_bare_xxx/\***
    - 添加了模板用户程序对 GPIO 的支持。

### Removed

- **无**

## [v0.7] - 2025-04-18
- 添加了 soc2018_bare_gpio_test 测试文件夹，用于提供 SoC2018 GPIO 测试程序,可在初始化GPIO程序中配置引脚方向等初始内容。
- main.c 中添加了 GPIO 的初始化函数、并对0、1号引脚进行拉高测试。

### Added

- **soc2018_bare_gpio_test/driver/src/gpio_driver.c**
    - 添加了 GPIO 驱动函数与ISR程序的实现。

- **soc2018_bare_gpio_test/driver/inc/gpio.h**
    - 添加了 GPIO 驱动函数声明与宏定义。

- **soc2018_bare_gpio_test/drivers/inc/gic.h**
    - 添加了对 SoC2018 GPIO 第0、1号引脚的中断支持。

- **soc2018_bare_gpio_test/drivers/src/gic.c**
    - 添加了对 SoC2018 GPIO 第1、2路中断的注册，使用统一的ISR程序进行注册。

### Changed

- **无**

### Removed

- **无**

## [v0.6] - 2025-03-21

- 更新了 soc2018_bare_xxx 模板，现在对开发者可以使用串口打印输出功能。注意：模板中的 SoC2018 UART 相关驱动为 soc2018_bare_uart_test 中 UART 驱动的修正版本，两者略有差异。
- 添加了 soc2018_bare_interrupts_test 测试文件夹，用于提供 soc2018 全局定时器、私有定时器、WDT 以及 FIQ 等测试程序。
- 添加了 soc2018_bare_trustzone_test 测试文件夹，用于提供 soc2018 TrustZone 测试程序。注意：TrustZone 测试程序的启动文件与模板中启动文件有所区别。

### Added

- **soc2018_bare_interrupts_test/\***
    - 用于提供 soc2018 全局定时器、私有定时器、WDT 以及 FIQ 等测试程序。

- **soc2018_bare_trustzone_test/\***
    - 用于提供 soc2018 TrustZone 测试程序。

- **soc2018_bare_xxx/drivers/inc/bsp_print.h**
    - 用于提供串口格式化输出函数声明与宏定义。

- **soc2018_bare_xxx/drivers/inc/uart.h**
    - 用于提供串口驱动函数声明与宏定义。

- **soc2018_bare_xxx/drivers/src/bsp_print.c**
    - 用于提供串口格式化输出函数声明与宏定义。

- **soc2018_bare_xxx/drivers/src/uart_driver.c**
    - 用于提供串口驱动函数声明与宏定义。

### Changed

- **soc2018_bare_xxx/drivers/inc/gic.h**
    - 添加了对 SoC2018 UART 的中断支持。

- **soc2018_bare_xxx/drivers/inc/types.h**
    - 添加了 SoC2018 `bsp_print.h` 及 `bsp_print.c` 使用的数据类型定义。

- **soc2018_bare_xxx/drivers/src/gic.c**
    - 添加了 SoC2018 UART 的中断注册。
    - 完善了中断处理逻辑，现在能够正确处理不同级别中断。

- **soc2018_bare_xxx/usr/src/main.c**
    - 现在头文件默认引入了 `bsp_print.h` 头文件。

### Removed

- **无**

## [v0.5] - 2025-03-21

- 添加了 soc2018_bare_uart_test 驱动文件夹，用于提供 soc2018 uart 外设的 IRQ 中断测试程序。
- 添加了 soc2018_bare_uart_test 格式化输出程序，用于提供 soc2018 uart 格式化输出函数。
- 添加了 soc2018_bare_uart_test 主程序，用于提供 soc2018 uart 格式化输出测试样例。

### Added

- **soc2018_bare_uart_test/drivers/inc/BSP_PRINT.h**
    - 用于提供串口格式化输出函数声明与宏定义。

- **soc2018_bare_uart_test/drivers/inc/BSP_UART.h**
    - 用于提供串口驱动函数声明与宏定义。

- **soc2018_bare_uart_test/drivers/inc/gic.h**
    - 与 soc2018_bare_sp804_test 测试文件夹一致。

- **soc2018_bare_uart_test/drivers/inc/sp804.h**
    - 与 soc2018_bare_sp804_test 测试文件夹一致。

- **soc2018_bare_uart_test/drivers/inc/types.h**
    - 与 soc2018_bare_sp804_test 测试文件夹一致。

- **soc2018_bare_uart_test/drivers/inc/UTILStd.h**
    - 用于提供串口格式化输出需要用到的类型定义。

- **soc2018_bare_uart_test/drivers/inc/BSP_PRINT.c**
    - 用于提供串口格式化输出函数实现。

- **soc2018_bare_uart_test/drivers/inc/BSP_UART.c**
    - 用于提供串口驱动函数实现。

- **soc2018_bare_uart_test/drivers/inc/gic.c**
    - 与 soc2018_bare_sp804_test 测试文件夹一致。

- **soc2018_bare_uart_test/drivers/inc/sp804_drives.h**
    - 与 soc2018_bare_sp804_test 测试文件夹一致。

### Changed

- **soc2018_bare_uart_test/Makefile**
    - 添加了头文件与源代码路径。

- **soc2018_bare_uart_test/usr/main.c**
    - 添加了串口格式化输出函数测试样例。

- **soc2018_bare_uart_test/usr/start.S**
    - 与 soc2018_bare_sp804_test 测试文件夹一致。

### Removed

- **无**

## [v0.4] - 2025-03-10

- 添加了 soc2018_bare_sp804_test 测试文件夹，用于提供 soc2018 SP804 外设的 IRQ 中断测试程序。
- 添加了 vexpress-a15_bare_irq_test 测试文件夹，用于提供 vexpress-a15 SP804 外设的 IRQ 中断测试程序。
- 添加了 vexpress-a9_bare_irq_test 测试文件夹，用于提供 vexpress-a9 SP804 外设的 IRQ 中断测试程序。
- 规范了 soc2018_bare_xxx 模板，修复了之前用户代码无法启用开发板中断的问题，添加了 GIC 初始化和中断使能设计等，现在对开发者添加外设驱动更加友好。
- 规范了所有用户程序代码的 Makefile 文件。

### Added

- **soc2018_bare_sp804_test/\***
    - 用于提供 soc2018 SP804 外设的 IRQ 中断测试程序。

- **soc2018_bare_xxx/\***
    - 重新添加了 soc2018_bare_xxx 模板的代码，现在所有的源文件都在对应模块的 src 目录中，所有头文件都在对应模块的 inc 目录中。

- **vexpress-a15_bare_irq_test/\***
    - 用于提供 vexpress-a15 SP804 外设的 IRQ 中断测试程序。

- **vexpress-a9_bare_irq_test/\***
    - 用于提供 vexpress-a9 SP804 外设的 IRQ 中断测试程序。

### Changed

- **\*/Makefile**
    - 删去了已有项目的 Makefile 中未使用的 INCDIRS 定义。
    - 解决了使用头文件的项目，正确定义 INCDIRS 后仍无法正确链接头文件的问题。

### Removed

- **soc2018_bare_xxx/usr/main.c**
    - 重新设计了模板代码的文件结构，旧代码被删除。

- **soc2018_bare_xxx/usr/start.S**
    - 重新设计了模板代码的文件结构，旧代码被删除。

## [v0.3] - 2025-02-27

- 添加了 soc2018_bare_exception_vector_test 测试文件夹，用于提供 soc2018 内核异常向量表及 BSS 段清零等功能测试。
- 更新了所有 soc2018 机器测试代码中的启动文件 start.S，现在 soc2018 机器运行时将支持异常向量表、MMU 等。
- 更新了所有 soc2018 机器测试代码中的连接脚本文件 link.ld，以配合启动文件的更新。
- 将所有机器测试代码中的 Makefile 文件的 GCC_HOME 变量定义为空，用户在使用时需自行填写。
- 修复了 CHANGELOG.md 中的一些问题。

### Added

- **soc2018_bare_exception_vector_test/usr/main.c**
    - 用于提供 soc2018 异常向量表代码与测试程序。

- **soc2018_bare_exception_vector_test/usr/start.S**

- **soc2018_bare_exception_vector_test/Makefile**

- **soc2018_bare_exception_vector_test/link.ld**

### Changed

- **soc2018_bare_float_test/Makefile**

- **soc2018_bare_float_test/link.ld**

- **soc2018_bare_float_test/usr/start.S**

- **soc2018_bare_uart_test/Makefile**

- **soc2018_bare_uart_test/link.ld**

- **soc2018_bare_uart_test/usr/start.S**

- **soc2018_bare_xxx/Makefile**

- **soc2018_bare_xxx/link.ld**

- **soc2018_bare_xxx/usr/start.S**

- **virt_bare_uart_test/Makefile**

### Removed

- **无**

## [v0.2] - 2025-02-25

- 添加 soc2018_bare_uart_test 测试文件夹，用于编写 soc2018 串口驱动并进行串口功能测试。

### Added

- **soc2018_bare_uart_test/usr/main.c**
    - 用于提供 soc2018 串口驱动代码与测试程序。

- **soc2018_bare_uart_test/usr/start.S**

- **soc2018_bare_uart_test/Makefile**

- **soc2018_bare_uart_test/link.ld**

### Changed

- **virt_bare_uart_test/usr/main.c**
    - 添加了 virt 开发板的串口读取驱动代码与对应的测试代码。

### Removed

- **无**

## [v0.1] - 2025-02-14

- 仓库建立。
- 初始仓库包含有两个被验证可使用的用户代码，以及一套用于 soc2018 机器裸机用户程序的模板，供后续开发使用。

### Added

- **CHANGELOG.md**
    - 用于提供 Changelog 支持。

- **README.md**
    - 提供使用教程。

- **.gitignore**
    - 用于设置不跟随 Git 管理的文件或目录。

<!-- 本文档修改注意事项 -->
<!-- 1.请在 VSCode 中修改本文档，并在进行任何提交前使用“Shift+Ctrl+V”快捷键预览渲染效果，查看撰写是否有误 -->
<!-- 2.请将 v0.1 版本的 Changelog 内容作为模板，确保格式正确，内容语言风格类似 -->
<!-- 3.请将新版本的日志写在旧版本的文本上方，保证本 Changelog 的日志顺序为“从新到旧” -->
<!-- 4.请注意中英文之间使用空格分隔，标点跟随上下文环境使用全角或半角 -->
