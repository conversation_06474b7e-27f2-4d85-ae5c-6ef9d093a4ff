/*
 * 适用于 virt 的串口测试程序
 */
#include <stdlib.h>
#include <stdint.h>
#include <float.h>

#define SOC2018_E_DEV_UART0 0x09000000
#define UART_TX (SOC2018_E_DEV_UART0 + 0x00) // 发送寄存器
#define UART_RX (SOC2018_E_DEV_UART0 + 0x00) // 接收寄存器

void uart_putc(char c)
{
  *(volatile uint32_t *)UART_TX = c; // 将字符写入发送寄存器
}

void uart_puts(const char *str)
{
  while (*str)
  {
    uart_putc(*str++); // 按顺序发送字符串
  }
}

// 串口接收字符
char uart_getc()
{
  return *(volatile uint32_t *)UART_RX; // 从接收寄存器读取字符
}

// 串口接收字符串
void uart_gets(char *buffer, int size)
{
  int i = 0;
  while (i < size - 1)
  {
    char c = uart_getc();
    // 遇到回车、换行、结束符就终止
    if (c == '\r' || c == '\n' || c == '\0')
    {
      break;
    }
    buffer[i++] = c;
  }
  buffer[i] = '\0';
}

int main()
{
  int i;
  int sum = 0;
  int arr[] = {1, 10, 4, 5, 6, 7};
  int n = sizeof(arr) / sizeof(arr[0]);
  for (i = 0; i < 2; ++i)
  {
    sum += arr[i];
  }
  double dbl = 1.5;
  double result = dbl * 2.5;

  uart_puts("this is uart drives output the end\n");
  uart_puts("this is uart test the end\n");

  char recv_temp = uart_getc();
  uart_puts("recv_temp: ");
  uart_putc(recv_temp);
  uart_putc('\n');

  char recv_buffer[32];
  uart_gets(recv_buffer, 32);
  uart_puts("recv_buffer: ");
  uart_puts(recv_buffer);
  uart_putc('\n');

  return 0;
}