/*
 * 基于 PMCCNTR + APB Timer 的性能测试
 */

#ifndef PERF_H
#define PERF_H

#include <stdint.h>

#define CPU_FREQ_HZ         400000000UL   /* 400 MHz */
#define APB_TIMER_FREQ_HZ   24000000UL    /* 24 MHz  */
#define APB_TIMER_BASE      0x10011000UL  /* APB Timer 基址 */
#define PMCCNTR_SAFE_LIMIT  0x80000000UL   /* 2^31 cycles ≈ 4.29 s @400 MHz */
#define APB_TIMER_VALUE  (*(volatile uint32_t *)(APB_TIMER_BASE + 0x04))

/* 回退标志 */
extern int global_flag;

static inline void pmu_enable_ccnt(void)
{
    uint32_t v;

    /* 允许用户态/非特权访问 PMU */
    asm volatile ("MRC p15, 0, %0, c9, c14, 0" : "=r"(v));
    v |= 1;                                 /* PMUSERENR.EN */
    asm volatile ("MCR p15, 0, %0, c9, c14, 0" :: "r"(v));

    /* 复位并启动 PMU */
    asm volatile ("MRC p15, 0, %0, c9, c12, 0" : "=r"(v));
    v |= 1u << 0;  /* E */
    v |= 1u << 2;  /* C */
    v |= 1u << 1;  /* P */
    asm volatile ("MCR p15, 0, %0, c9, c12, 0" :: "r"(v));

    /* 仅打开 Cycle Counter */
    v = 1u << 31;
    asm volatile ("MCR p15, 0, %0, c9, c12, 1" :: "r"(v));

    asm volatile ("ISB");
}

static inline uint32_t pmu_read_ccnt(void)
{
    uint32_t cc;
    asm volatile ("MRC p15, 0, %0, c9, c13, 0" : "=r"(cc));
    return cc;
}

static inline uint32_t apb_timer_read(void)
{
    return 0xFFFFFFFFu - APB_TIMER_VALUE;
}

static inline uint64_t cycles_to_ns(uint32_t cycles)
{
    return ((uint64_t)cycles * 1000000000ULL) / CPU_FREQ_HZ;
}

static inline uint64_t apb_to_ns(uint32_t ticks)
{
    return ((uint64_t)ticks  * 1000000000ULL) / APB_TIMER_FREQ_HZ;
}

static inline uint64_t perf_time_ns(void (*func)(void))
{
    uint32_t apb_start = apb_timer_read();
    uint32_t cc_start  = pmu_read_ccnt();
    func();
    uint32_t cc_end  = pmu_read_ccnt();
    uint32_t apb_end = apb_timer_read();

    /* 先尝试用 PMCCNTR */
    uint32_t cc_delta = cc_end - cc_start;
    if (cc_delta < PMCCNTR_SAFE_LIMIT) {
        return cycles_to_ns(cc_delta);
    } else {
        /* 超时则退回 APB Timer */
        global_flag = 1;

        uint32_t apb_delta = apb_end - apb_start;
        return apb_to_ns(apb_delta);
    }
}

#endif /* PERF_H */
