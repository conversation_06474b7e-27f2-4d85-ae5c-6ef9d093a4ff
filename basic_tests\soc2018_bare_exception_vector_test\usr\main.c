/*
 * 适用于 soc2018 的异常向量表测试程序
 */
 #include <math.h>

/* 定义未初始化的全局变量，测试 BSS 段 */
int global_var;  // 未初始化的全局变量

static int static_var;  // 未初始化的静态变量

void trigger_svc_exception(void)
{
    __asm__ volatile ("svc #0");
}

void trigger_undefined_exception(void)
{
    __asm__ volatile (".word 0xFFFFFFFF");
}

void trigger_data_abort(void)
{
    volatile int *bad_ptr = (int *)0x0;  // 0地址通常不可访问
    int value = *bad_ptr;
    (void)value;
}

int main(void)
{
    /* BSS 段变量测试 */
    int global_var_reciver = global_var; // 变量值为0
    int static_var_reciver = static_var; // 变量值为0

    /* 整数算术运算测试 */
    int a = 10, b = 3, err = 0;
    int sum   = a + b;
    int diff  = a - b;
    int prod  = a * b;
    int div   = a / b;
    int mod   = a % b;

    /* 递增、递减与复合赋值 */
    int i = 0;
    i++;
    i--;
    int tmp = a / err;

    int c = 5;
    c += 3;
    c *= 2;
    c -= 4;
    c /= 2;

    /* 位运算测试 */
    int d = 8, e = 3;
    int bit_and = d & e;
    int bit_or  = d | e;
    int bit_xor = d ^ e;
    int shift_left = d << 2;
    int shift_right = d >> 1;

    /* 浮点数算术运算测试 */
    double x = 10.5, y = 3.55;
    double f_sum  = x + y;
    double f_diff = x - y;
    double f_prod = x * y;
    double f_div  = x / y;
    double f_mod  = fmod(x, y);  // 浮点数取模

    /* 三角函数测试 */
    double angle_deg = 45.0;
    double angle_rad = angle_deg * (M_PI / 180.0);
    double sin_val = sin(angle_rad);
    double cos_val = cos(angle_rad);
    double tan_val = tan(angle_rad);

    /* 指数、对数与幂运算测试 */
    double exp_val = exp(1.0);
    double log_val = log(exp_val);
    double pow_val = pow(2.0, 8.0);

    /* 混合运算测试 */
    int m = 7;
    double d_m = m;  // 整型到浮点型的转换
    double mixed_result = d_m / 2.0;

    trigger_undefined_exception();
    // vtrigger_data_abort();
    // trigger_svc_exception();

    /* 浮点数的递增测试 触发异常后永远不会执行到此处 */
    double f = 1.0;
    f += 0.1;

    return 0;
}
