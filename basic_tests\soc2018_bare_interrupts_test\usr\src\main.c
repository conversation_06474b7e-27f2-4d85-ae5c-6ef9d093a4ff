/*
 * 适用于 soc2018 的全局定时器、私有定时器、WDT 以及 FIQ 等测试程序
 */

#include "types.h"
#include "gic.h"
#include "sp804.h"
#include "privtimers.h"

extern int global_timer_irq_count;
extern int private_timer_irq_count;
extern int watchdog_timer_irq_count;

int main(void)
{
    int final = 0;

    /* GIC 初始化 */
    gic_init();
    
    /* 在此处撰写业务代码 */
    // global_timer_count_test();
    // global_timer_compare_test();
    // private_timer_count_test();
    // private_timer_load_test();
    // watchdog_count_test();
    // watchdog_irq_test();
    WRITE_32(TIMER_LOAD, 1000000);                                       // 加载计数值为 1000000 （1s 触发一次中断） sp804 的频率为 1MHz 即 1us 计数一次
    WRITE_32(TIMER_CTRL, (1 << 7) | (1 << 6) | (1 << 5) | (0 << 2));
    // WRITE_32(TIMER_LOAD_2, 1500000);                                       // 加载计数值为 1000000 （1s 触发一次中断） sp804 的频率为 1MHz 即 1us 计数一次
    // WRITE_32(TIMER_CTRL_2, (1 << 7) | (1 << 6) | (1 << 5) | (0 << 2));
    // watchdog_reset_test();

    final = 1;
    
    return 0;
}
