#ifndef __DRV_Def_Msg_H__
#define __DRV_Def_Msg_H__

#include "UTILStd.h"

#include "DRV_BIT.h"

#define DRV_AD_All_Num 	(4)	 //���ݼ���ģʽ��ʹ�ã����������AD�ɼ���·��


#define DRV_DA_All_Num 	(15)	 //���ݼ���ģʽ��ʹ�ã����������DA�ɼ���·��


#define DRV_DAKB_All_Num 	(14)	 //���ݼ���ģʽ��ʹ�ã����������DA�ɼ���·��


#define DRV_ADKB_All_Num 	(160)	 //���ݼ���ģʽ��ʹ�ã����������AD�ɼ���·��


#define DRV_Power_All_Num 	(5)	 //���ݼ���ģʽ��ʹ�ã���������������·��


#define DRV_IO_NUM0	 (0)	//��0��IO��
#define DRV_IO_NUM1	 (1)	//��1��IO��
#define DRV_IO_NUM2	 (2)	//��2��IO��


#define DRV_Def_AD_SPI_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_AD_SSI_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_BUS_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_CAN_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_CSB_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_DA_9726_CLOCK_KB_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_DA_9762_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_DA_9762KB_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_DA_BUS_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_DA_GPIO_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_DA_SSI_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_DA_RESET_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_Power_Mode2_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_Power_Mode3_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_IO_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_SPI_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_TM_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_Power_Mode1_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_INT_GPI_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_BIT_IN_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_BIT_OUT_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_Eight_FRE_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_Latch_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_SSI_OUT_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_SSI_IN_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_Syn_Source_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_Power_Mode4_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_UART_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_Time_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_Count_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_PULSE_OUT_Type	(DRVUSED)	 //ʹ�ø÷�ʽ


#define DRV_Def_CAN_INT_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_TM_INT_Type	(DRVUSED)	 //ʹ�ø÷�ʽ
#define DRV_Def_Eight_INT_Type	(DRVUSED)	 //ʹ�ø÷�ʽ


#define DRV_Def_AD_SPI_Num	(2)	 //��·��
#define DRV_Def_AD_SSI_Num	(2)	 //��·��
#define DRV_Def_BUS_Num	(1)	 //��·��
#define DRV_Def_CAN_Num	(4)	 //��·��
#define DRV_Def_CSB_Num	(1)	 //��·��
#define DRV_Def_DA_9726_CLOCK_KB_Num	(1)	 //��·��
#define DRV_Def_DA_9762_Num	(1)	 //��·��
#define DRV_Def_DA_9762KB_Num	(1)	 //��·��
#define DRV_Def_DA_BUS_Num	(2)	 //��·��
#define DRV_Def_DA_GPIO_Num	(6)	 //��·��
#define DRV_Def_DA_SSI_Num	(4)	 //��·��
#define DRV_Def_DA_RESET_Num	(1)	 //��·��
#define DRV_Def_Power_Mode2_Num	(2)	 //��·��
#define DRV_Def_Power_Mode3_Num	(1)	 //��·��
#define DRV_Def_IO_Num	(3)	 //��·��
#define DRV_Def_SPI_Num	(2)	 //��·��
#define DRV_Def_TM_Num	(1)	 //��·��
#define DRV_Def_Power_Mode1_Num	(1)	 //��·��
#define DRV_Def_INT_GPI_Num	(1)	 //��·��
#define DRV_Def_BIT_IN_Num	(2)	 //��·��
#define DRV_Def_BIT_OUT_Num	(9)	 //��·��
#define DRV_Def_Eight_FRE_Num	(1)	 //��·��
#define DRV_Def_Latch_Num	(1)	 //��·��
#define DRV_Def_SSI_OUT_Num	(2)	 //��·��
#define DRV_Def_SSI_IN_Num	(3)	 //��·��
#define DRV_Def_Syn_Source_Num	(2)	 //��·��
#define DRV_Def_Power_Mode4_Num	(1)	 //��·��
#define DRV_Def_UART_Num	(4)	 //��·��
#define DRV_Def_Time_Num	(2)	 //��·��
#define DRV_Def_Count_Num	(1)	 //��·��
#define DRV_Def_PULSE_OUT_Num	(6)	 //��·��


#endif
