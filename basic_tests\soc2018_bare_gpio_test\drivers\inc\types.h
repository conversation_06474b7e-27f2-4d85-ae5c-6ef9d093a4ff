/*
 * 寄存器操作宏及数据类型定义头文件
 */

#ifndef TYPES_H
#define TYPES_H

#include <stdint.h>

/* 寄存器操作宏 */
#define READ_8(addr)                (*(volatile uint8_t*)(addr))
#define READ_16(addr)               (*(volatile uint16_t*)(addr))
#define READ_32(addr)               (*(volatile uint32_t*)(addr))
#define WRITE_8(addr, value)        (*(volatile uint8_t*)(addr) = (uint8_t)(value))
#define WRITE_16(addr, value)       (*(volatile uint16_t*)(addr) = (uint16_t)(value))
#define WRITE_32(addr, value)       (*(volatile uint32_t*)(addr) = (value))

/* 数据类型定义 */
typedef	unsigned char		U8;
typedef	unsigned short		U16;
typedef	unsigned int		U32;
typedef	unsigned long long	U64;

typedef volatile U32		V_U32;
typedef	volatile float		V_F32;

#endif /* TYPES_H */
