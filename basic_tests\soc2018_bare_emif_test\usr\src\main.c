/*
 * 适用于 soc2018 的 EMIF 控制器及 EDAC 功能测试程序
 */

#include "types.h"
#include "gic.h"        /* 中断控制功能 */
#include "bsp_print.h"  /* 串口输出功能 */
#include "emif.h"
#include "uart.h"      /* 串口驱动功能 */

extern int irq_count;

void emif_base_test(void) {
    uart_puts("Hello EDAC TEST!\n");

    uart_puts("Hello EMIF TEST!\n");

    /* --------EMIF功能函数测试-------- */
    /* EMIF初始化 */
    Bsp_EmifInit();

    /*使能/禁止SRAM区EDAC*/
    // Bsp_SetEmifRamEdac(BSP_ENABLE);

    // Bsp_SetEmifRamEdac(BSP_DISABLE);

    /*读旁路使能/禁止*/
    // Bsp_EmifSetRamEdacReadByPass(BSP_ENABLE);

    // Bsp_EmifSetRamEdacReadByPass(BSP_DISABLE);

    /*写旁路使能/禁止*/
    // Bsp_EmifSetRamEdacWriteByPass(BSP_ENABLE);

    // Bsp_EmifSetRamEdacWriteByPass(BSP_DISABLE);

    /*使能/禁止ROM区EDAC*/
    // Bsp_SetRomEdac(BSP_ENABLE);

    // Bsp_SetRomEdac(BSP_DISABLE);

    /*设置ROM区位宽*/
    print2("set ROM data weight to 32bit\n");
    Bsp_SetRomWidth(EMIF_PROM_WIDTH_32);
    U32 width = Bsp_GetRomWidth();
    print2("ROM data weight: %d\n", width);

    print2("set ROM data weight to 16bit\n");
    Bsp_SetRomWidth(EMIF_PROM_WIDTH_16);
    width = Bsp_GetRomWidth();
    print2("ROM data weight: %d\n", width);

    print2("set ROM data weight to 8bit\n");
    Bsp_SetRomWidth(EMIF_PROM_WIDTH_8);
    width = Bsp_GetRomWidth();
    print2("ROM data weight: %d\n", width);

    /* EMIF中断测试 */
    //  触发单错中断
    // *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EMIF_INT_CLEAR_REG) = 0x00000001;
    //  触发多错中断
    // *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EMIF_INT_CLEAR_REG) = 0x00000002;
    //  触发IO中断
    // *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EMIF_INT_CLEAR_REG) = 0x00000004;

    /* EDAC访存行为中断触发测试 */
    // volatile unsigned int *ptr = (volatile unsigned int *)0x90000000;
    // for (int i = 0; i < 100; i++) {
    //     *ptr++ = 0xFFFFFFFF;
    // }

    /* --------EMIF功能函数测试-------- */

    /* EMIF寄存器写入测试 */
    // *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + WRMEM_CONFIG_REG) = 0x00000001;
    // *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + ORMEM_CONFIG_REG) = 0x00000002;
    // *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + IO_CONFIG_REG) = 0x00000003;
    // *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG) = 0x00000004;
    // *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + IO_RDY_TIMEOUT_REG) = 0x00000005;
    // *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + WR_WS_CFG_REG) = 0x00000007;
    // *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + OR_WS_CFG_REG) = 0x00000008;
    // *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + IO_WS_CFG_REG) = 0x00000009;
    // *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + CERR_ADDR_REG) = 0x0000000A;
    // *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + CERR_DATA_REG) = 0x0000000B;
    // *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + CERR_EDAC_REG) = 0x0000000C;
    // *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + DERR_ADDR_REG) = 0x0000000D;
    // *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + DERR_DATA_REG) = 0x0000000E;
    // *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + DERR_EDAC_REG) = 0x0000000F;

    // /* EMIF寄存器读取测试 */
    // U32 wrmem_config = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + WRMEM_CONFIG_REG);
    // U32 ormem_config = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + ORMEM_CONFIG_REG);
    // U32 io_config = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + IO_CONFIG_REG);
    // U32 edac_config = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG);
    // U32 io_rdy_timeout = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + IO_RDY_TIMEOUT_REG);
    // U32 emif_int_clear = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + EMIF_INT_CLEAR_REG);
    // U32 wr_ws_cfg = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + WR_WS_CFG_REG);
    // U32 or_ws_cfg = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + OR_WS_CFG_REG);
    // U32 io_ws_cfg = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + IO_WS_CFG_REG);
    // U32 cerr_addr = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + CERR_ADDR_REG);
    // U32 cerr_data = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + CERR_DATA_REG);
    // U32 cerr_edac = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + CERR_EDAC_REG);
    // U32 derr_addr = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + DERR_ADDR_REG);
    // U32 derr_data = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + DERR_DATA_REG);
    // U32 derr_edac = *(V_U32 *)(BSP_EMIF_REG_BASE_ADDR + DERR_EDAC_REG);
    // print2("print2 test\n");
    // print2("WRMEM_CONFIG_REG: 0x%d\n", wrmem_config);
    // print2("ORMEM_CONFIG_REG: 0x%d\n", ormem_config);
    // print2("IO_CONFIG_REG: 0x%d\n", io_config);
    // print2("EDAC_CONFIG_REG: 0x%d\n", edac_config);
    // print2("IO_RDY_TIMEOUT_REG: 0x%d\n", io_rdy_timeout);
    // print2("EMIF_INT_CLEAR_REG: 0x%d\n", emif_int_clear);
    // print2("WR_WS_CFG_REG: 0x%d\n", wr_ws_cfg);
    // print2("OR_WS_CFG_REG: 0x%d\n", or_ws_cfg);
    // print2("IO_WS_CFG_REG: 0x%d\n", io_ws_cfg);
    // print2("CERR_ADDR_REG: 0x%d\n", cerr_addr);
    // print2("CERR_DATA_REG: 0x%d\n", cerr_data);
    // print2("CERR_EDAC_REG: 0x%d\n", cerr_edac);
    // print2("DERR_ADDR_REG: 0x%d\n", derr_addr);
    // print2("DERR_DATA_REG: 0x%d\n", derr_data);
    // print2("DERR_EDAC_REG: 0x%d\n", derr_edac);

    // volatile unsigned int *ptr = (volatile unsigned int *)0x90000000;
    // for (int i = 0; i < 100; i++) {
    //     *ptr++ = 0xFFFFFFFF;
    // }

    // /* QOM 禁用 EDAC */
    // volatile unsigned int *edac_ctl_mem = (volatile unsigned int *)0x20000000;
    // *edac_ctl_mem = 0x1;

    // for (int i = 0; i < 100; i++) {
    //     *ptr++ = 0xFFFFFFFF;
    // }
    
    // /* QOM 使能 EDAC */
    // int value = *edac_ctl_mem;

    // for (int i = 0; i < 100; i++) {
    //     *ptr++ = 0xFFFFFFFF;
    // }
}

void qemu_ram_rmw_test(void) {
    volatile uint8_t  * ptr1 = (volatile uint8_t *) 0xC0000010;
    volatile uint32_t * ptr2 = (volatile uint32_t *) 0xC0000010;

    /* 
     * 内存区域 RMW 测试
     */
    *ptr2 = 0xFFFFFFFF;     // 整字写
    uint32_t val = *ptr2;
    // val == 0xFFFFFFFF

    val = *ptr1;            // 字节读
    // val == 0xFF

    *ptr1 = 0x00;           // 字节写
    val = *ptr2;

    // 具有 RMW 特性

    volatile uint16_t * ptr3 = (volatile uint16_t *) 0xC0000010;
    *ptr3 = 0x1122;           // 半字写
    val = *ptr2;

    // 具有 RMW 特性

    // ptr2 = (volatile uint32_t *) 0xC0000011;
    // *ptr2 = 0x11;
    // 触发 Data_Abort
    
    ptr1 = (volatile uint8_t *) 0xC0000011;
    *ptr1 = 0x00;
    ptr1 = (volatile uint8_t *) 0xC0000012;
    *ptr1 = 0xCC;
    val = *ptr2;

    // 具有 RMW 特性
}

void qemu_reg_rmw_test(void) {
    volatile uint8_t  * ptr1 = (volatile uint8_t *) 0xA00A0000;
    volatile uint32_t * ptr2 = (volatile uint32_t *) 0xA00A0000;

    /* 
     * 寄存器区域 RMW 测试
     */
    *ptr2 = 0xFFFFFFFF;     // 整字写
    uint32_t val = *ptr2;
    // val == 0xFFFFFFFF

    val = *ptr1;            // 字节读
    // val == 0xFF

    *ptr1 = 0x00;           // 字节写
    val = *ptr2;

    // 不具有 RMW 特性

    volatile uint16_t * ptr3 = (volatile uint16_t *) 0xA00A0000;
    *ptr3 = 0x1122;           // 半字写
    val = *ptr2;

    // 不具有 RMW 特性

    // ptr2 = (volatile uint32_t *) 0xC0000011;
    // *ptr2 = 0x11;
    // 触发 Data_Abort
    
    ptr1 = (volatile uint8_t *) 0xA00A0001;
    *ptr1 = 0x00;
    ptr1 = (volatile uint8_t *) 0xA00A0002;
    *ptr1 = 0xCC;
    val = *ptr2;

    // 不具有 RMW 特性
}

void emif_basic_reg_test() {
    print2("========== emif_basic_test ==========\n");

    /* 查看 ROM 当前配置 */
    uint32_t rom_conf = *(uint32_t *)(BSP_EMIF_REG_BASE_ADDR + ORMEM_CONFIG_REG);
    print2("rom_conf is: 0x%x\n", rom_conf);

    // 分析当前 ROM 位宽以及容量是否正确

    /* 查看 RAM 当前配置 */
    uint32_t ram_conf = *(uint32_t *)(BSP_EMIF_REG_BASE_ADDR + WRMEM_CONFIG_REG);
    print2("ram_conf is: 0x%x\n", ram_conf);

    // 分析当前 RAM 位宽以及容量是否正确

    /* 查看 EDAC 当前配置 */
    uint32_t edac_conf = *(uint32_t *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG);
    print2("edac_conf is: 0x%x\n\n", edac_conf);

    /* 使用 BSP 的初始化函数 */
    Bsp_EmifInit();
    print2("EMIF initialized successfully\n");

    /* 查看 ROM 当前配置 */
    rom_conf = *(uint32_t *)(BSP_EMIF_REG_BASE_ADDR + ORMEM_CONFIG_REG);
    print2("rom_conf is: 0x%x\n", rom_conf);

    // 分析当前 ROM 位宽以及容量是否正确 8bit 256M

    /* 查看 RAM 当前配置 */
    ram_conf = *(uint32_t *)(BSP_EMIF_REG_BASE_ADDR + WRMEM_CONFIG_REG);
    print2("ram_conf is: 0x%x\n", ram_conf);

    // 分析当前 RAM 位宽以及容量是否正确 32bit 2M

    /* 查看 EDAC 当前配置 */
    edac_conf = *(uint32_t *)(BSP_EMIF_REG_BASE_ADDR + EDAC_CONFIG_REG);
    print2("edac_conf is: 0x%x\n\n", edac_conf);

    // 分析当前 RAM、ROM 位宽以及容量是否正确
}

void emif_basic_access_test() {
    print2("========== emif_basic_access_test ==========\n");
    /* EMIF RAM 空间访存测试 */
    print2("EMIF RAM Memory Access TEST\n");
    volatile uint32_t *ptr = (volatile uint32_t *)0x90000000; /* 使用 EMIF 测试专用内存进行 */
    *ptr++ = 0xFFFFFFFF;
    *ptr = 0xFFFFFFFF;

    /* 读取以测试是否写入成功 */
    uint32_t val2 = *ptr--;
    uint32_t val1 = *ptr;
    print2("val1 is: 0x%x\n", val1);
    print2("val2 is: 0x%x\n", val2);
}

/*
 * 32bit 位宽 EMIF RAM EDAC 测试
 */
void emif_32bit_ram_edac_test() {
    Bsp_EmifInit(); /* 使用 BSP 的初始化函数 32bit 2MB RAM EDAC 使能 */
    print2("EMIF initialized successfully\n");

    volatile uint32_t *ptr = (volatile uint32_t *)0x90000000; /* 使用 EMIF 测试专用内存进行 */
    *ptr++ = 0xFFFFFFFF;
    *ptr = 0xFFFFFFFF;

    /*
     * 基本读写 EDAC 测试 
     */
    ptr = (volatile uint32_t *)0x90000000;
    int val1 = *ptr++;
    int val2 = *ptr++;
    int val3 = *ptr;
    
    // check no error

    print2("val1 is: 0x%x\n", val1); // 0xFFFFFFFF
    print2("val2 is: 0x%x\n", val2); // 0xFFFFFFFF
    print2("val3 is: 0x%x\n", val3); // 0

    /* 
     * 读写旁路测试
     */
    uint32_t *ram_writebypass_addr = (uint32_t *)(BSP_EMIF_REG_BASE_ADDR + 0x5C);
    uint32_t *ram_writebypass_edac = (uint32_t *)(BSP_EMIF_REG_BASE_ADDR + 0x60);
    uint32_t *ram_readbypass_addr = (uint32_t *)(BSP_EMIF_REG_BASE_ADDR + 0x50);
    uint32_t *ram_readbypass_edac = (uint32_t *)(BSP_EMIF_REG_BASE_ADDR + 0x54);
    uint32_t *ram_readbypass_data = (uint32_t *)(BSP_EMIF_REG_BASE_ADDR + 0x58);

    /* 查看复位写旁路地址是否正确 */
    uint32_t addr = *ram_readbypass_addr;
    print2("addr is: 0x%x\n", addr); // 0xA00A0050

    /* 配置写旁路 */
    *ram_writebypass_addr = 0x90000000; // 地址为 0x90000000
    *ram_writebypass_edac = 0xFF; // 校验码为 0xFF

    /* 使能 RAM 写旁路 */
    Bsp_EmifSetRamEdacWriteByPass(BSP_ENABLE); 

    /* 触发写旁路 */
    ptr = (volatile uint32_t *)0x90000000;
    *ptr = 0x12345678;  // 触发写旁路

    // 成功触发写旁路

    /* 配置读旁路 */
    *ram_readbypass_addr = 0x90000000; // 地址为 0x90000000

    /* 使能读旁路 */
    Bsp_EmifSetRamEdacReadByPass(BSP_ENABLE); 

    /* 触发读旁路 */
    ptr = (volatile uint32_t *)0x90000000;
    val1 = *ptr;  // 触发读旁路

    // 成功触发读旁路

    /* 查看读旁路寄存器 */
    uint32_t read_data = *ram_readbypass_data;
    uint32_t read_edac = *ram_readbypass_edac;
    print2("read_data is: 0x%x\n", read_data); // 0x12345678
    print2("read_edac is: 0x%x\n", read_edac); // 0xFF

    /* 访问 0x90000004 不会触发写旁路和读旁路 */
    ptr = (volatile uint32_t *)0x90000004;
    *ptr = 0x87654321;
    val2 = *ptr;

    // no error

    /* 读旁路修改为 0x90000004 测试是否触发 */
    *ram_readbypass_addr = 0x90000004;
    val2 = *ptr;  // 触发读旁路
    read_data = *ram_readbypass_data;
    read_edac = *ram_readbypass_edac;
    print2("read_data is: 0x%x\n", read_data); // 0x87654321
    print2("read_edac is: 0x%x\n", read_edac); // 0x2A

    /* 写旁路修改为 0x90000004 测试并通过读旁路验证 */
    *ram_writebypass_addr = 0x90000004;
    *ptr = 0;  // 触发写旁路
    val2 = *ptr;
    read_data = *ram_readbypass_data;
    read_edac = *ram_readbypass_edac;
    print2("read_data is: 0x%x\n", read_data); // 0x0
    print2("read_edac is: 0x%x\n", read_edac); // 0xFF

    /* 关闭读写旁路使能 */
    Bsp_EmifSetRamEdacReadByPass(BSP_DISABLE);
    Bsp_EmifSetRamEdacWriteByPass(BSP_DISABLE); 

    ptr = (volatile uint32_t *)0x90000000;
    *ptr = 0x3A3B4C4D;
    val1 = *ptr; // 不报错
    ptr = (volatile uint32_t *)0x90000004;
    val2 = *ptr;

    // 因校验码和数据不符，报错
}

/*
 * 16bit 位宽 EMIF RAM EDAC 测试
 */
void emif_16bit_ram_edac_test() {
    Bsp_EmifInit(); /* 使用 BSP 的初始化函数 32bit 2MB RAM EDAC 使能 */
    *(volatile uint32_t *)(BSP_EMIF_REG_BASE_ADDR + WRMEM_CONFIG_REG) &= ~0xC000;
    *(volatile uint32_t *)(BSP_EMIF_REG_BASE_ADDR + WRMEM_CONFIG_REG) |= 0x4000; // 设置为 16bit 位宽

    print2("EMIF initialized successfully\n");

    volatile uint16_t *ptr = (volatile uint16_t *)0x90000000; /* 使用 EMIF 测试专用内存进行 */
    *ptr++ = 0xFFFF;
    *ptr++ = 0xFFFF;
    *ptr = 0xFFFF;

    /*
     * 基本读写 EDAC 测试 
     */
    ptr = (volatile uint16_t *)0x90000000;
    int val1 = *ptr++;
    int val2 = *ptr++;
    int val3 = *ptr++;
    int val4 = *ptr;
    
    // check no error

    print2("val1 is: 0x%x\n", val1); // 0xFFFF
    print2("val2 is: 0x%x\n", val2); // 0xFFFF
    print2("val3 is: 0x%x\n", val3); // 0xFFFF
    print2("val4 is: 0x%x\n", val4); // 0x0000

    /* 
     * 读写旁路测试
     */
    volatile uint32_t *ram_writebypass_addr = (volatile uint32_t *)(BSP_EMIF_REG_BASE_ADDR + 0x5C);
    volatile uint32_t *ram_writebypass_edac = (volatile uint32_t *)(BSP_EMIF_REG_BASE_ADDR + 0x60);
    volatile uint32_t *ram_readbypass_addr = (volatile uint32_t *)(BSP_EMIF_REG_BASE_ADDR + 0x50);
    volatile uint32_t *ram_readbypass_edac = (volatile uint32_t *)(BSP_EMIF_REG_BASE_ADDR + 0x54);
    volatile uint32_t *ram_readbypass_data = (volatile uint32_t *)(BSP_EMIF_REG_BASE_ADDR + 0x58);

    /* 查看复位写旁路地址是否正确 */
    uint32_t addr = *ram_readbypass_addr;
    print2("addr is: 0x%x\n", addr); // 0xA00A0050

    /* 配置写旁路 */
    *ram_writebypass_addr = 0x90000000; // 地址为 0x90000000
    *ram_writebypass_edac = 0xFF; // 校验码为 0xFF

    /* 使能 RAM 写旁路 */
    Bsp_EmifSetRamEdacWriteByPass(BSP_ENABLE); 

    /* 触发写旁路 */
    ptr = (volatile uint16_t *)0x90000000;
    *ptr = 0x1234;  // 触发写旁路

    // 成功触发写旁路

    /* 配置读旁路 */
    *ram_readbypass_addr = 0x90000000; // 地址为 0x90000000

    /* 使能读旁路 */
    Bsp_EmifSetRamEdacReadByPass(BSP_ENABLE); 

    /* 触发读旁路 */
    ptr = (volatile uint16_t *)0x90000000;
    val1 = *ptr;  // 触发读旁路

    // 成功触发读旁路

    /* 查看读旁路寄存器 */
    uint32_t read_data = *ram_readbypass_data;
    uint32_t read_edac = *ram_readbypass_edac;
    print2("read_data is: 0x%x\n", read_data); // 0xFFFF1234
    print2("read_edac is: 0x%x\n", read_edac); // 0xFF

    /* 访问 0x90000002 不会触发写旁路和读旁路 */
    ptr = (volatile uint16_t *)0x90000002;
    *ptr = 0x5678;
    val2 = *ptr;

    // no error 此时校验码被修正为 0x82

    /* 读旁路修改为 0x90000004 测试是否触发 */
    *ram_readbypass_addr = 0x90000004;
    ptr = (volatile uint16_t *)0x90000004;
    val2 = *ptr;  // 触发读旁路
    read_data = *ram_readbypass_data;
    read_edac = *ram_readbypass_edac;
    print2("read_data is: 0x%x\n", read_data); // 0x0000FFFF
    print2("read_edac is: 0x%x\n", read_edac); // 0x41

    /* 写旁路修改为 0x90000004 测试并通过读旁路验证 */
    *ram_writebypass_addr = 0x90000004;
    *ptr = 0xAAAA;  // 触发写旁路
    val2 = *ptr;
    read_data = *ram_readbypass_data;
    read_edac = *ram_readbypass_edac;
    print2("read_data is: 0x%x\n", read_data); // 0x0000AAAA
    print2("read_edac is: 0x%x\n", read_edac); // 0xFF

    /* 关闭读写旁路使能 */
    Bsp_EmifSetRamEdacReadByPass(BSP_DISABLE);
    Bsp_EmifSetRamEdacWriteByPass(BSP_DISABLE); 

    ptr = (volatile uint16_t *)0x90000002;
    *ptr = 0x3A3B;
    val1 = *ptr; // 不报错
    ptr = (volatile uint16_t *)0x90000000;
    *ptr = 0x3A3B;
    val1 = *ptr; // 不报错
    ptr = (volatile uint16_t *)0x90000004;
    val2 = *ptr;

    // 因校验码和数据不符，报错
}

/*
 * 8bit 位宽 EMIF RAM EDAC 测试
 */
void emif_8bit_ram_edac_test() {
    Bsp_EmifInit(); /* 使用 BSP 的初始化函数 32bit 2MB RAM EDAC 使能 */
    *(volatile uint32_t *)(BSP_EMIF_REG_BASE_ADDR + WRMEM_CONFIG_REG) &= ~0xC000;
    *(volatile uint32_t *)(BSP_EMIF_REG_BASE_ADDR + WRMEM_CONFIG_REG) |= 0x0000; // 设置为 16bit 位宽

    print2("EMIF initialized successfully\n");

    volatile uint8_t *ptr = (volatile uint8_t *)0x90000000; /* 使用 EMIF 测试专用内存进行 */
    *ptr++ = 0xFF;
    *ptr = 0xFF;

    /*
     * 基本读写 EDAC 测试 
     */
    ptr = (volatile uint8_t *)0x90000000;
    int val1 = *ptr++;
    int val2 = *ptr++;
    int val3 = *ptr;
    
    // check no error

    print2("val1 is: 0x%x\n", val1); // 0xFF
    print2("val2 is: 0x%x\n", val2); // 0xFF
    print2("val3 is: 0x%x\n", val3); // 0

    /* 
     * 读写旁路测试
     */
    volatile uint32_t *ram_writebypass_addr = (volatile uint32_t *)(BSP_EMIF_REG_BASE_ADDR + 0x5C);
    volatile uint32_t *ram_writebypass_edac = (volatile uint32_t *)(BSP_EMIF_REG_BASE_ADDR + 0x60);
    volatile uint32_t *ram_readbypass_addr = (volatile uint32_t *)(BSP_EMIF_REG_BASE_ADDR + 0x50);
    volatile uint32_t *ram_readbypass_edac = (volatile uint32_t *)(BSP_EMIF_REG_BASE_ADDR + 0x54);
    volatile uint32_t *ram_readbypass_data = (volatile uint32_t *)(BSP_EMIF_REG_BASE_ADDR + 0x58);

    /* 查看复位写旁路地址是否正确 */
    uint32_t addr = *ram_readbypass_addr;
    print2("addr is: 0x%x\n", addr); // 0xA00A0050

    /* 配置写旁路 */
    *ram_writebypass_addr = 0x90000000; // 地址为 0x90000000
    *ram_writebypass_edac = 0xFF; // 校验码为 0xFF

    /* 使能 RAM 写旁路 */
    Bsp_EmifSetRamEdacWriteByPass(BSP_ENABLE); 

    /* 触发写旁路 */
    ptr = (volatile uint8_t *)0x90000000;
    *ptr = 0xFF;  // 触发写旁路

    // 成功触发写旁路

    /* 配置读旁路 */
    *ram_readbypass_addr = 0x90000000; // 地址为 0x90000000

    /* 使能读旁路 */
    Bsp_EmifSetRamEdacReadByPass(BSP_ENABLE); 

    /* 触发读旁路 */
    ptr = (volatile uint8_t *)0x90000000;
    val1 = *ptr;  // 触发读旁路

    // 成功触发读旁路

    /* 查看读旁路寄存器 */
    uint32_t read_data = *ram_readbypass_data;
    uint32_t read_edac = *ram_readbypass_edac;
    print2("read_data is: 0x%x\n", read_data); // 0x0000FFFF
    print2("read_edac is: 0x%x\n", read_edac); // 0xFF

    /* 访问 0x90000003 不会触发写旁路和读旁路 */
    ptr = (volatile uint8_t *)0x90000003;
    *ptr = 0xCC;
    val2 = *ptr;

    // no error

    /* 读旁路修改为 0x90000004 测试是否触发 */
    *ram_readbypass_addr = 0x90000004;
    ptr = (volatile uint8_t *)0x90000004;
    val2 = *ptr;  // 触发读旁路
    read_data = *ram_readbypass_data;
    read_edac = *ram_readbypass_edac;
    print2("read_data is: 0x%x\n", read_data); // 0x00000000
    print2("read_edac is: 0x%x\n", read_edac); // 0xC

    /* 写旁路修改为 0x90000004 测试并通过读旁路验证 */
    *ram_writebypass_addr = 0x90000004;
    *ptr = 0xAB;  // 触发写旁路
    val2 = *ptr;
    read_data = *ram_readbypass_data;
    read_edac = *ram_readbypass_edac;
    print2("read_data is: 0x%x\n", read_data); // 0x000000AB
    print2("read_edac is: 0x%x\n", read_edac); // 0xFF

    /* 关闭读写旁路使能 */
    Bsp_EmifSetRamEdacReadByPass(BSP_DISABLE);
    Bsp_EmifSetRamEdacWriteByPass(BSP_DISABLE); 

    ptr = (volatile uint8_t *)0x90000002;
    *ptr = 0xCD;
    val1 = *ptr; // 不报错
    ptr = (volatile uint8_t *)0x90000004;
    val2 = *ptr;

    // 因校验码和数据不符，报错
}

/*
 * EMIF 校验码单错测试
 */
void emif_edac_onebit_err_test() {
    Bsp_EmifInit(); /* 使用 BSP 的初始化函数 32bit 2MB RAM EDAC 使能 */
    print2("EMIF initialized successfully\n");

    volatile uint32_t *ptr = (volatile uint32_t *)0x90000000; /* 使用 EMIF 测试专用内存进行 */
    *ptr++ = 0xFFFFFFFF;
    *ptr = 0xFFFFFFFF;

    /*
     * 基本读写 EDAC 测试 
     */
    ptr = (volatile uint32_t *)0x90000000;
    int val1 = *ptr++;
    int val2 = *ptr++;
    int val3 = *ptr;
    
    // check no error

    print2("val1 is: 0x%x\n", val1); // 0xFFFFFFFF
    print2("val2 is: 0x%x\n", val2); // 0xFFFFFFFF
    print2("val3 is: 0x%x\n", val3); // 0

    /* 
     * 读写旁路相关寄存器
     */
    uint32_t *ram_writebypass_addr = (uint32_t *)(BSP_EMIF_REG_BASE_ADDR + 0x5C);
    uint32_t *ram_writebypass_edac = (uint32_t *)(BSP_EMIF_REG_BASE_ADDR + 0x60);
    uint32_t *ram_readbypass_addr = (uint32_t *)(BSP_EMIF_REG_BASE_ADDR + 0x50);
    uint32_t *ram_readbypass_edac = (uint32_t *)(BSP_EMIF_REG_BASE_ADDR + 0x54);
    uint32_t *ram_readbypass_data = (uint32_t *)(BSP_EMIF_REG_BASE_ADDR + 0x58);

    /* 读旁路读出 0x90000000 的校验码 */
    *ram_readbypass_addr = 0x90000000;
    Bsp_EmifSetRamEdacReadByPass(BSP_ENABLE);

    ptr = (volatile uint32_t *)0x90000000;
    val1 = *ptr; // 触发读旁路
    uint8_t edac = *ram_readbypass_edac; // 获取 EDAC 校验码
    print2("EDAC code: 0d%u\n", edac);

    /* 造单错 */
    edac ^= (1u << 3);

    /* 写旁路写回 0x90000000 的单错校验码 */
    *ram_writebypass_addr = 0x90000000;
    *ram_writebypass_edac = (uint32_t)edac;
    Bsp_EmifSetRamEdacWriteByPass(BSP_ENABLE);

    ptr = (volatile uint32_t *)0x90000000;
    *ptr = val1; // 触发写旁路，写回

    Bsp_EmifSetRamEdacReadByPass(BSP_DISABLE);
    Bsp_EmifSetRamEdacWriteByPass(BSP_DISABLE);

    /* 通过加载数据查看是否能检出单错，是否检出成功 */
    val1 = *ptr; // 检出单错，自动修正
    val1 = *ptr; // 不检出错误
}

int main(void)
{
    /* GIC 初始化 */
    gic_init();
    
    /* 业务代码开始 */
    // emif_basic_reg_test();
    // emif_basic_access_test();
    // emif_32bit_ram_edac_test();
    // emif_16bit_ram_edac_test();
    // emif_8bit_ram_edac_test();
    emif_edac_onebit_err_test();

    // qemu_ram_rmw_test();
    // qemu_reg_rmw_test();
    // emif_base_test();
    /* 业务代码结束 */

    return 0;
}
