/* ***************************************************************************
 *		(c) Copyright 2008 - , the 8th Laboratory of BICE
 *
 * 文件名：	wdt.c
 * 创建日期：2023-6-20
 * 版本：	1.00
 * 功能：	提供基于cpu上的狗的相关驱动程序
 * 软件支持环境:Linux操作系统平台下的GNU工具包
 * 硬件支持环境:Soc2018处理器
 * ****************************************************************************


*/
#include "BSPWdt.h"

/*
 ****************************************************************************
 *
 * 名称：Bsp_CpuWdtSet
 *
 * 功能：设置处理器内部看门狗狗咬时间。
 *
 * 参数：U32 wdtTime:狗咬时间
 *
 * *****************************************************************************
*/
void Bsp_CpuWdtSet(U32 wdtTime)
{
	V_U32 *timeScaler;
	U32 preScaler;
	U32 countSet;

	Bsp_CpuWdtStop();
	Bsp_CpuWdtClearResetStatus();

	Bsp_CpuWdtModeSet();	//set to wtdog mode

	/*set scaler*/
	preScaler = (WTDTIMER_FREQ >> WTDTIMER_FREQ_DIVIDED_PARAM) - 1; 

	timeScaler = (V_U32 *)(BSP_AMBA_WDT_BASEADDR + BSP_WDT_CONTROL_OFFSET_REG);
	*timeScaler = (*timeScaler & 0xffff00ff) | ((preScaler&0xff) << 8);


	/*set counter*/
	countSet = (wdtTime << WTDTIMER_FREQ_DIVIDED_PARAM)-1;
	
	*(V_U32 *)(BSP_AMBA_WDT_BASEADDR + BSP_WDT_RELOAD_OFFSET_REG) = countSet;	
	// *(V_U32 *)(BSP_AMBA_WDT_BASEADDR + BSP_WDT_COUNT_OFFSET_REG) = countSet;	
}


/*
 ****************************************************************************
 *
 * 名称：Bsp_CpuWdtEnable
 *
 * 功能：启动处理器内部看门狗。
 *
 *
 * *****************************************************************************
*/
void Bsp_CpuWdtEnable(void)
{
	Bsp_CpuWdtSet(WDT_RESET_TIME);
	*(V_U32 *)(BSP_AMBA_WDT_BASEADDR + BSP_WDT_CONTROL_OFFSET_REG) = *(V_U32 *)(BSP_AMBA_WDT_BASEADDR + BSP_WDT_CONTROL_OFFSET_REG) | 0x1;
}

/*
 ****************************************************************************
 *
 * 名称：Bsp_CpuWdtStop
 *
 * 功能：禁止处理器内部看门狗
 *
 * *****************************************************************************
*/
void Bsp_CpuWdtStop(void)
{
	*(V_U32 *)(BSP_AMBA_WDT_BASEADDR + BSP_WDT_CONTROL_OFFSET_REG) = *(V_U32 *)(BSP_AMBA_WDT_BASEADDR + BSP_WDT_CONTROL_OFFSET_REG) & 0xfffffffe; 
}

/*
 ****************************************************************************
 *
 * 名称：Bsp_CpuWdtClear
 *
 * 功能：清处理器内部看门狗。
 *
 * 参数：wdtTime，看门狗时间，当量us
 *
 * *****************************************************************************
*/
void Bsp_CpuWdtClear(U32 wdtTime)
{
	*(V_U32 *)(BSP_AMBA_WDT_BASEADDR + BSP_WDT_RELOAD_OFFSET_REG) = (wdtTime << WTDTIMER_FREQ_DIVIDED_PARAM)-1;
}

/*
****************************************************************************
* 名	称：Bsp_CpuWdtDisable
* 功	能：设置看门狗为Timer模式：需要对WD Disable Reg进行如下操作，直接对看门狗控制寄存器中的看门狗模式位写0无效
* 说明：软件对看门狗禁用寄存器依次写入0x12345678和0x87564321，看门狗模式被设置为0。要重新激活看门狗，软件需要向看门狗控制寄存器中的看门狗模式写入1
*****************************************************************************
*/
void Bsp_CpuWdtDisable(void)
{
	U32 oldlevel;
	/* 关中断 */
	// oldlevel = Bsp_DisableInt(ALL_USR_ISR);
	*(V_U32 *)(BSP_AMBA_WDT_BASEADDR + BSP_WDT_FORBIDDEN_OFFSET_REG) = 0x12345678;
	*(V_U32 *)(BSP_AMBA_WDT_BASEADDR + BSP_WDT_FORBIDDEN_OFFSET_REG) = 0x87654321;
	/* 开中断 */
	// Bsp_EnableInt(oldlevel);

	Bsp_CpuWdtStop();//after changing to timer mode,must stop enable bit,or won't stop
}

/*
 ****************************************************************************
 *
 * 名称：Bsp_CpuWdtClearResetStatus
 *
 * 功能：清除WDT复位状态标志
 *
 * *****************************************************************************
*/
void Bsp_CpuWdtClearResetStatus(void)
{
	*(V_U32 *)(BSP_AMBA_WDT_BASEADDR + BSP_WDT_RESET_STATUS_OFFSET_REG) = 0x1;
}

/*
 ****************************************************************************
 *
 * 名称：Bsp_CpuWdtModeSet
 *
 * 功能：设置成WTD工作模式
 *
 *******************************************************
*/
void Bsp_CpuWdtModeSet(void)
{
	*(V_U32 *)(BSP_AMBA_WDT_BASEADDR + BSP_WDT_CONTROL_OFFSET_REG) = *(V_U32 *)(BSP_AMBA_WDT_BASEADDR + BSP_WDT_CONTROL_OFFSET_REG) | 0x8;
}


/*
 ****************************************************************************
 *
 * 名称：Bsp_CpuWdtGetResetStatus
 *
 * 功能：获取WDT复位状态标志
 *
 * 返回值:	0x1 有看门狗复位，0x0无看门狗复位
 *
 * *****************************************************************************
*/
U32 Bsp_CpuWdtGetResetStatus(void)
{
	return ((*(V_U32 *)(BSP_AMBA_WDT_BASEADDR + BSP_WDT_RESET_STATUS_OFFSET_REG))&0x1);
}


