/*
 * 适用于 soc2018 的浮点数执行测试程序
 */
#include <math.h>

int main(void)
{
    /* 整数算术运算测试 */
    int a = 10, b = 3;
    int sum   = a + b;
    int diff  = a - b;
    int prod  = a * b;
    int div   = a / b;
    int mod   = a % b;

    /* 递增、递减与复合赋值 */
    int i = 0;
    i++;
    i--;

    int c = 5;
    c += 3;
    c *= 2;
    c -= 4;
    c /= 2;

    /* 位运算测试 */
    int d = 8, e = 3;
    int bit_and = d & e;
    int bit_or  = d | e;
    int bit_xor = d ^ e;
    int shift_left = d << 2;
    int shift_right = d >> 1;

    /* 浮点数算术运算测试 */
    double x = 10.5, y = 3.55;
    double f_sum  = x + y;
    double f_diff = x - y;
    double f_prod = x * y;
    double f_div  = x / y;
    double f_mod  = fmod(x, y);  // 浮点数取模

    /* 三角函数测试 */
    double angle_deg = 45.0;
    double angle_rad = angle_deg * (M_PI / 180.0);
    double sin_val = sin(angle_rad);
    double cos_val = cos(angle_rad);
    double tan_val = tan(angle_rad);

    /* 指数、对数与幂运算测试 */
    double exp_val = exp(1.0);
    double log_val = log(exp_val);
    double pow_val = pow(2.0, 8.0);

    /* 混合运算测试 */
    int m = 7;
    double d_m = m;  // 整型到浮点型的转换
    double mixed_result = d_m / 2.0;

    /* 浮点数的递增测试 */
    double f = 1.0;
    f += 0.1;

    return 0;
}
