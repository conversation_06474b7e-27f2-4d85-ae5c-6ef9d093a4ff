#ifndef __DRV_BIT_H__
#define __DRV_BIT_H__

#include "UTILStd.h"

///// ?????????????????????????///////
#define DRV_BITIN_IO_PULSE_WidthAdd1 (200000)
#define DRV_BITIN_IO_PULSE_WidthAdd2 (200000)

#define  DRVTRUE  (1)
#define  DRVFALSE  (0)

#define  DRVENABLE  (1)
#define  DRVDISABLE  (0)

#define  DRVUSED  (1)
#define  DRVUNUSED  (0)


#define  DRVREV  (1)
#define  DRVUNREV  (0)

#define  DRV_PULSE_PERIOD  (1)
#define  DRV_PULSE_SINGLE  (0)

#define  DRVHIGH (1)
#define  DRVLOW (0)

#define  DRVVARAANT  (1)//???????
#define  DRVINVARAANT  (0)//???????

#define  DRV_A  (0)//??1??can/csb/TM
#define  DRV_B (1)//??2??can/csb/TM

#define  DRV_FIRST_SAMP  (0)//SPI ??????1???????????????
#define  DRV_SECOND_SAMP (1) //SPI ??????2???????????????

///// ???????(??????????)///////

#define SYS_CLOCK  (0x0)//?????
#define ONE_US   (0x1)   // 1 us 
#define TEN_US   (0x2)   // 10 us 
#define HUNDRED_US   (0x3)   // 100 us 

///// ???????????(??????????)///////

//??????????
#define  DRV_INT_EDGE_TRIG  (0)//???????
#define  DRV_INT_PULSE_TRIG (3)//???????
#define  DRV_INT_HIGH_TRIG  (1)//???????????????
#define  DRV_INT_LOW_TRIG (2)//??????????????

#define DRV_INT_ASIC0         (0)
#define DRV_INT_ASIC1         (1)
#define DRV_INT_ASIC2         (2)
#define DRV_INT_DISABLE       (0x91D0200A)
#define DRV_INT_ENABLE         (0xEB90146F)

#define  DRV_CLK_16M (16)//????16M
#define  DRV_CLK_20M (20)//????20M
#define  DRV_CLK_100M (100)//????100M

#define  DRV_CLK_20M_PERIOD (50)//????20M????????50ns
#define  DRV_CLK_100M_PERIOD (10)//????100M????????10ns


#define  DRV_NO_ERROR  (0xffffffff)//?????????????no??????????????????
#define  DRV_CAN_NO_ERROR  (0xffffeeee) //????????
#define  DRV_STRUCT_MEMBER_ERROR  (0xeeeeeeee)//???????????????????????????
#define  DRV_TIME_NO_ERROR  (0xffffffffffffffff)//????????????????no???????????



#define  DRV_BITIN_IO_PULSE  (0) //?????????:IO??????????????
#define  DRV_BITIN_PM_GPIO  (2)//????????? PM ??GPIO????
#define  DRV_BITIN_IO_GPIO  (1) //????????? IO??GPIO????

#define  DRV_BITOUT_PM_GPIO  (2)//?????????? ??????GPIO????
#define  DRV_BITOUT_IO_GPIO  (1) //?????????? 6017 GPIO????

#define  DRV_GPS_OUTPUT_ADDR  (0x10C8)//GPS?????????
#define  DRV_SYN_SOURCE_SOURCE_ADDR   (0x100C) //??????????????????????

//???????????
#define  DRV_SYN_SOURCE_EQU_ADDR  (0x1040)//??????????
#define  DRV_SYN_SOURCE_SELECT_ADDR  (0x1044)//??????????
#define  DRV_SYN_SOURCE_VALID_ADDR  (0x1140)//???????????????
#define  DRV_SYN_SOURCE_PULSE_ADDR  (0x10C0)//??????????????
#define  DRV_SYN_SOURCE_PHASE_ADDR  (0x1070)//????????????
#define  DRV_SYN_SOURCE_WIDTH_ADDR  (0x1090)//??????????
#define  DRV_SYN_SOURCE_PERIOD_ADDR  (0x10B0)//??????????
#define  DRV_SYN_SOURCE_OUTPUT_ADDR  (0x10C4)//??????

/* GPIO??????????????? ??1????GPO????0?????GPO?? */
#define DRV_BIT_DIR_ADDR            (0x00) //D(15:0)???GPIO(15:0)

/* GPIO???????????? ??00:????? 01:1us  10:10us  11:100us */
#define DRV_BIT_FIL_0_ADDR          (0x14)  //D(1:0):GPIO(7:0)??????????D(3:2):GPIO(15:8)?????????????????
#define DRV_BIT_FIL_1_ADDR          (0x18)  //D(1:0):GPIO(71:64)??????????D(3:2):GPIO(79:72)??????????

/* GPI??????????? ????????????GPI???????+1??*??????? */
#define DRV_BIT_FILWID_ADDR          (0x1c)  //???????GPIO(7:0)
/* GPIO?????????????  */
#define DRV_BIT_IN_ADDR            (0x44) //??????GPIO(15:0)

/* ????????????????????????  */
#define DRV_GPS0_FLAG_TIME_ADDR            (0x1104) //GPS0
#define DRV_GPS1_FLAG_TIME_ADDR            (0x1108) //GPS1
#define DRV_GPS2_FLAG_TIME_ADDR           (0x110c) //GPS2
#define DRV_GPS3_FLAG_TIME_ADDR            (0x1110) //GPS3
#define DRV_SYN_SOURCE0_FLAG_TIME_ADDR            (0x1114)//SYN_SOURCE0
#define DRV_SYN_SOURCE1_FLAG_TIME_ADDR            (0x1118) //SYN_SOURCE1
#define DRV_SYN_SOURCE2_FLAG_TIME_ADDR           (0x111c) //SYN_SOURCE2
#define DRV_SYN_SOURCE3_FLAG_TIME_ADDR            (0x1120) //SYN_SOURCE3

/* ??????????????  */
#define DRV_SYN_SOURCE_FLAG_ADDR                 (0x1100)

/* GPIO????/????????????? ??1?????????0??????? */
#define DRV_BIT_SELOUT_ADDR          (0x180) //D(15:0)???GPIO(63:48)

/* GPIO???????????  */
#define DRV_BIT_OUT_ADDR            (0x188) //??????GPIO(15:0)

/* ????????  */
#define DRV_INT_MODE_ADDR            (0xD80) //???????????
#define DRV_INT_SEL_ADDR(i)              (0xD40+0xC*(i)) //???????????
#define DRV_INT_ENABLE_ADDR           (0xC40) //???????????
#define DRV_INT_STATE_ADDR               (0xC00) //???????????

#define DRV_INT_UART_RECV_ADDR           (0xC80) //?????????????????????
#define DRV_INT_SSI_RECV_ADDR              (0xCC0) //??????????????????????
#define DRV_INT_TM_SEND_ADDR              (0xD00) //TM??????????????????????


#define  DRV_Get(addr)                              (*(V_U8 *)((U32)(addr)))
#define  DRV_Set(addr,value)                    (*(V_U8 *)((U32)(addr))=(value))
#define  DRV_Tr32(data1,data2,data3)      ((((U32)(data1)) & ((U32)(data2)))|(((U32)(data1)) & ((U32)(data3)))|(((U32)(data2)) & ((U32)(data3))))
#define  DRV_Set_High(addr,bit)                (*(V_U8 *)((U32)(addr)) |= (0x1<<((U32)(bit)))) //??bit???1????????????
#define  DRV_Set_Low(addr,bit)                 (*(V_U8 *)((U32)(addr)) &= (~(0x1<<((U32)(bit)))))//??bit???0????????????  

/* define - ???? */
#ifdef DEBUG
#define DRV_ERR_SET_ERRNO(errno)         do {DRVErrNo = (errno);  print2("\r\nDRVErrNo: 0x%x",DRVErrNo); } while(0)
#else
#define DRV_ERR_SET_ERRNO(errno)         (DRVErrNo = (errno))
#endif
//extern U32 DRVErrNo;
U32 DRVErrNo;
/*drv??errno?? */
#define DRV_ERR_BIT_IN                   (0x1 << 8)
#define DRV_ERR_BIT_OUT                (0x2 << 8)
#define DRV_ERR_SYN_SOURCE          (0x3 << 8)
#define DRV_ERR_CAN                       (0x4 << 8)
#define DRV_ERR_POWER                   (0x5 << 8)
#define DRV_ERR_TIME                      (0x6 << 8)
#define DRV_ERR_UART                      (0x7 << 8)
#define DRV_ERR_CSB                        (0x8 << 8)
#define DRV_ERR_TM                          (0x9 << 8)
#define DRV_ERR_PULSE                     (0xA << 8)
#define DRV_ERR_COUNT                      (0xB << 8)
#define DRV_ERR_LATCH                      (0xC << 8)
#define DRV_ERR_SPI                          (0xD << 8)
#define DRV_ERR_SSI                           (0xE << 8)
#define DRV_ERR_AD                            (0xF << 8)
#define DRV_ERR_DA                            (0x10<< 8)
#define DRV_ERR_INT                            (0x11<< 8)
#define DRV_ERR_BUS                            (0x12<< 8)
#define DRV_ERR_PULSE_PERIOD_WIDTH                     (0x13 << 8) // add by lcl
#define DRV_ERR_EIGHT                           (0x14 << 8)
#define DRV_ERR_EQU                            (0x15 << 8)             
#define DRV_ERR_IO                           (0x16 << 8)             

#define DRV_ERR_BIT_IN_PASS_PARAMETER_NO                    (DRV_ERR_BIT_IN |  0x1) /* ????????NO???? */
#define DRV_ERR_BIT_IN_STRUCT_MEMBER_GATHER             (DRV_ERR_BIT_IN  | 0x2) /*?????? ???????(????????)???? */

#define DRV_ERR_BIT_OUT_PASS_PARAMETER_NO                 (DRV_ERR_BIT_OUT |  0x1) /* ?????????NO???? */
#define DRV_ERR_BIT_OUT_PASS_PARAMETER_VALUE             (DRV_ERR_BIT_OUT |  0x2) /* ?????????value???? */

#define DRV_ERR_SYN_SOURCE_PASS_PARAMETER_NO            (DRV_ERR_SYN_SOURCE |  0x1) /* ?????????NO???? */
#define DRV_ERR_SYN_SOURCE_PASS_PARAMETER_PERIOD    (DRV_ERR_SYN_SOURCE |  0x2) /* ?????????period???? */
#define DRV_ERR_SYN_SOURCE_PASS_PARAMETER_SELECT    (DRV_ERR_SYN_SOURCE |  0x3) /* ?????????select???? */
#define DRV_ERR_SYN_SOURCE_PASS_PARAMETER_PHASE    (DRV_ERR_SYN_SOURCE |  0x4) /* ?????????phase???? */
#define DRV_ERR_SYN_SOURCE_PASS_PARAMETER_WIDTH    (DRV_ERR_SYN_SOURCE |  0x5) /* ?????????width???? */

#define DRV_ERR_CAN_PASS_PARAMETER_NO                       (DRV_ERR_CAN |  0x1) /* CAN????NO???? */
#define DRV_ERR_CAN_PASS_PARAMETER_BAUD                     (DRV_ERR_CAN |  0x2) /* CAN????BAUD???? */

#define DRV_ERR_POWER_PASS_PARAMETER_NO                    (DRV_ERR_POWER |  0x1) /* ??????????NO???? */
#define DRV_ERR_POWER_PASS_PARAMETER_ACTION              (DRV_ERR_POWER |  0x2) /* ??????????action???? */

#define DRV_ERR_TIME_PASS_PARAMETER_NO                       (DRV_ERR_TIME |  0x1) /* ?????????NO???? */
#define DRV_ERR_TIME_PASS_PARAMETER_TIME                     (DRV_ERR_TIME |  0x2) /* ??????????????????????????? */
#define DRV_ERR_TIME_PASS_PARAMETER_GPS                      (DRV_ERR_TIME |  0x3) /* ?????????NO???? */
#define DRV_ERR_EIGHT_PASS_PARAMETER_NO                       (DRV_ERR_EIGHT |  0x1) /*???????????NO???? */

#define DRV_ERR_UART_PASS_PARAMETER_NO                        (DRV_ERR_UART |  0x1) /* UART????NO???? */

#define DRV_ERR_CSB_PASS_PARAMETER_NO                         (DRV_ERR_CSB |  0x1) /* CSB????NO???? */

#define DRV_ERR_TM_PASS_PARAMETER_NO                             (DRV_ERR_TM |  0x1) /*TM????NO???? */
#define DRV_ERR_TM_PASS_PARAMETER_BAUD                        (DRV_ERR_TM |  0x2) /* TM????BAUD???? */

#define DRV_ERR_PULSE_PASS_PARAMETER_NO                    (DRV_ERR_PULSE |  0x1) /* ??????????????NO???? */

#define DRV_ERR_PULSE_PASS_PARAMETER_DEFEND                    (DRV_ERR_PULSE |  0x2) /* ???????????????????????? */

#define DRV_ERR_COUNT_PASS_PARAMETER_NO                    (DRV_ERR_COUNT |  0x1) /* ???????????NO???? */

#define DRV_ERR_LATCH_PASS_PARAMETER_NO                    (DRV_ERR_LATCH |  0x1) /* ????????????NO???? */

#define DRV_ERR_SPI_PASS_PARAMETER_NO                    (DRV_ERR_SPI |  0x1) /* SPI????NO???? */
#define DRV_ERR_SPI_PASS_PARAMETER_INTRECV                    (DRV_ERR_SPI |  0x2) /* SPI???????? */

#define DRV_ERR_SSI_PASS_PARAMETER_NO                    (DRV_ERR_SSI |  0x1) /* SSI????NO???? */
#define DRV_ERR_SSI_PASS_PARAMETER_MODE                    (DRV_ERR_SSI |  0x2) /* SSI????NO???? */
#define DRV_ERR_SSI_PASS_PARAMETER_INTRECV                    (DRV_ERR_SSI |  0x3) /* SSI???????? */

#define DRV_ERR_AD_PASS_PARAMETER_NO                              (DRV_ERR_AD  | 0x1)/* AD????NO???? */
#define DRV_ERR_AD_STRUCT_MEMBER_MODE                          (DRV_ERR_AD  | 0x2) /*AD???????(??????)????????????????????SPI?? */

#define DRV_ERR_DA_PASS_PARAMETER_NO                              (DRV_ERR_DA  | 0x1)/* DA????NO???? */
#define DRV_ERR_DA_DEFINE_MODE                                           (DRV_ERR_DA  | 0x2)/* DAdefine ????MODE???? */


#define DRV_ERR_INT_PASS_PARAMETER_INTNO                         (DRV_ERR_INT  | 0x1)/* INT????INTNO???? */
#define DRV_ERR_INT_PASS_PARAMETER_ASICNO                         (DRV_ERR_INT  | 0x2)/* INT????INTNO???? */
#define DRV_ERR_INT_PASS_PARAMETER_MODE                         (DRV_ERR_INT  | 0x3)/* INT????INTNO???? */
#define DRV_ERR_INT_PASS_PARAMETER_NO                         (DRV_ERR_INT  | 0x4)/* INT???? NO???? */

#define DRV_ERR_BUS_PASS_PARAMETER_ADDR                        (DRV_ERR_BUS  | 0x1)/* BUS????ADDR???? */

#define DRV_ERR_EQU_PASS_PARAMETER_ADDR                        (DRV_ERR_EQU  | 0x1)/* ????????????????????????? */

#define DRV_ERR_IO_PASS_PARAMETER_ADDR                        (DRV_ERR_IO  | 0x1)     /*IO?????? */

///////////////////BITIN??????????


extern double  Drv_Convert_Equ(U32 num, U32 ioNum);

extern U32 Drv_Replace_Value_Toregister(U32 addrorreg, U32 value, U32 start, U32 end);
extern void Drv_IO_Struct_Init(void);



#endif
