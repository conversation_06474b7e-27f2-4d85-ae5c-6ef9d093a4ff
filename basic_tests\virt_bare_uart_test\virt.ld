/* gnu gcc linker script virt.ld */
ENTRY(_start)

SECTIONS
{
    . = 0x00000000,
    .text : {
        *(vectors);
        *(.text);
    }
    .rodata : {
        *(.rodata);
    }
    flash_sdata = .;

    . = 0x40000000,
    ram_sdata = .;

    .data : AT (flash_sdata) {
        *(.data);
    }
    ram_edata = .;
    data_size = ram_edata - ram_sdata;

    sbss = .;
    __bss_start__ = .;
    .bss : {
        *(.bss*);
    }
    ebss = .;
    __bss_end__ = .;
    bss_size = ebss - sbss;
}