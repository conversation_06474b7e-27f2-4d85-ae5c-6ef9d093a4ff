#ifndef __UART_H__
#define __UART_H__

#include "common.h"

typedef struct
{
        __IO uint32_t MULTI0;    //0x00
	__IO uint32_t MULTI1;    //0x04
	__IO uint32_t MULTI2;    //0x08
	__IO uint32_t LCR;       //0x0c
	__IO uint32_t MCR;       //0x10
	__IO uint32_t LSR;       //0x14
	__IO uint32_t MSR;       //0x18
	__IO uint32_t SCR;       //0x1c
	__IO uint32_t LPDLL;     //0x20
	__IO uint32_t LPDLH;     //0x24
        __IO uint32_t RESERVED0[2];
	__IO uint32_t SRBR;      //0x30
	__IO uint32_t RESERVED1[15];
	__IO uint32_t FAR;       //0x70
	__IO uint32_t TFR;       //0x74
	__IO uint32_t RFW;       //0x78
	__IO uint32_t USR;       //0x7c
	__IO uint32_t TFL;       //0x80
	__IO uint32_t RFL;       //0x84
	__IO uint32_t SRR;       //0x88
	__IO uint32_t SRTS;      //0x8c
	__IO uint32_t SBCR;      //0x90
	__IO uint32_t SDMAM;     //0x94
	__IO uint32_t SFE;       //0x98
	__IO uint32_t SRT;       //0x9c
	__IO uint32_t STET;      //0xA0
	__IO uint32_t HTX;       //0xA4
	__IO uint32_t DMASA;     //0xA8
	__IO uint32_t TCR;       //0xAC
	__IO uint32_t DE_EN;     //0xB0
	__IO uint32_t RE_EN;     //0xB4
	__IO uint32_t DET;       //0xB8
	__IO uint32_t TAT;       //0xBC
	__IO uint32_t DLF;       //0xC0
	__IO uint32_t RAR;       //0xC4
	__IO uint32_t TAR;       //0xC8
	__IO uint32_t LCR_EXT;   //0xCC
	__IO uint32_t RESERVED2[9];
	__IO uint32_t CPR;       //0xF4
	__IO uint32_t UCV;       //0xF8
	__IO uint32_t CTR;       //0xFC
} UART_TypeDef;

#define UART_MULTI0_REG(base)  ((base)->MULTI0)
#define UART_MULTI1_REG(base)  ((base)->MULTI1)
#define UART_MULTI2_REG(base)  ((base)->MULTI2)
#define UART_LCR_REG(base)     ((base)->LCR)
#define UART_MCR_REG(base)     ((base)->MCR)
#define UART_LSR_REG(base)     ((base)->LSR)
#define UART_MSR_REG(base)     ((base)->MSR)
#define UART_SCR_REG(base)     ((base)->SCR)
#define UART_LPDLL_REG(base)   ((base)->LPDLL)
#define UART_LPDLH_REG(base)   ((base)->LPDLH)
#define UART_SRBR_REG(base)    ((base)->SRBR)
#define UART_FAR_REG(base)     ((base)->FAR)
#define UART_TFR_REG(base)     ((base)->TFR)
#define UART_RFW_REG(base)     ((base)->RFW)
#define UART_USR_REG(base)     ((base)->USR)
#define UART_TFL_REG(base)     ((base)->TFL)
#define UART_RFL_REG(base)     ((base)->RFL)
#define UART_SRR_REG(base)     ((base)->SRR)
#define UART_SRTS_REG(base)    ((base)->SRTS)
#define UART_SBCR_REG(base)    ((base)->SBCR)
#define UART_SDMAM_REG(base)   ((base)->SDMAM)
#define UART_SFE_REG(base)     ((base)->SFE)
#define UART_SRT_REG(base)     ((base)->SRT)
#define UART_STET_REG(base)    ((base)->STET)
#define UART_HTX_REG(base)     ((base)->HTX)
#define UART_DMASA_REG(base)   ((base)->DMASA)
#define UART_TCR_REG(base)     ((base)->TCR)
#define UART_DE_EN_REG(base)   ((base)->DE_EN)
#define UART_RE_EN_REG(base)   ((base)->RE_EN)
#define UART_DET_REG(base)     ((base)->DET)
#define UART_TAT_REG(base)     ((base)->TAT)
#define UART_DLF_REG(base)     ((base)->DLF)
#define UART_RAR_REG(base)     ((base)->RAR)
#define UART_TAR_REG(base)     ((base)->TAR)
#define UART_LCR_EXT_REG(base) ((base)->LCR_EXT)
#define UART_CPR_REG(base)     ((base)->CPR)
#define UART_UCV_REG(base)     ((base)->UCV)
#define UART_CTR_REG(base)     ((base)->CTR)

//uart base physical address
#define UART0_BASE_PTR    ((UART_TypeDef*)(UART_BASE_PTR))
#define UART1_BASE_PTR    ((UART_TypeDef*)(UART_BASE_PTR+0x1000))
#define UART2_BASE_PTR    ((UART_TypeDef*)(UART_BASE_PTR+0x2000))
#define UART3_BASE_PTR    ((UART_TypeDef*)(UART_BASE_PTR+0x3000))
#define UART4_BASE_PTR    ((UART_TypeDef*)(UART_BASE_PTR+0x4000))
#define UART5_BASE_PTR    ((UART_TypeDef*)(UART_BASE_PTR+0x5000))
#define UART6_BASE_PTR    ((UART_TypeDef*)(UART_BASE_PTR+0x6000))
#define UART7_BASE_PTR    ((UART_TypeDef*)(UART_BASE_PTR+0x7000))
#define UART8_BASE_PTR    ((UART_TypeDef*)(UART_BASE_PTR+0x8000))
#define UART9_BASE_PTR    ((UART_TypeDef*)(UART_BASE_PTR+0x9000))
#define UART10_BASE_PTR   ((UART_TypeDef*)(UART_BASE_PTR+0xA000))
#define UART11_BASE_PTR   ((UART_TypeDef*)(UART_BASE_PTR+0xB000))

//uart ID
typedef enum
{
	UART0,
	UART1,
	UART2,
	UART3,
	UART4,
	UART5,
	UART6,
	UART7,
	UART8,
	UART9,
	UART10,
	UART11
} UARTn;

//Interrupt ID
typedef enum
{
	RX_DATA_AVALIABLE = (0x1<<0),   //Received Data Available Interrupt
	TX_HOLDING_EMPTY = (0x1<<1),    //Transmit Holding Register Empty Interrupt
	RX_LINE_STATUS = (0x1<<2),      //Receiver line status interrupt
	MODEM_STATUS_INT = (0x1<<3),    //Modem status interrupt
	THRE_INT = (0x1<<7),           //THRE interrupt
    NONE_INT = (0x1<<8)            //do not use interrupt
} UART_Intn;

//NOTE:uart FIFO 512 byte
typedef enum
{
	RX_FIFO_1,     //1 character
	RX_FIFO_128,   //FIFO ¼ full
	RX_FIFO_256,   //FIFO ½ full
	RX_FIFO_510    //FIFO 2 less than full
}RX_Trigger;

typedef enum
{
	TX_FIFO_1,     //1 character
	TX_FIFO_128,   //FIFO ¼ full
	TX_FIFO_256,   //FIFO ½ full
	TX_FIFO_510    //FIFO 2 less than full
}TX_Trigger;

void UART_BaudRateSet(UARTn uartn, uint32_t baudrate);

//enable corresponding interrupt
void uart_Int_enable(UARTn uartn, UART_Intn uart_int);

//disable corresponding interrupt
void uart_Int_disable(UARTn uartn, UART_Intn uart_int);

//return value: 0(FIFO disabled), 1(FIFO enabled)
uint8_t uart_get_FIFO_set(UARTn uartn);

//return value :1(at least one parity, framing or break indication error in the FIFO)
//              0(no error)
uint8_t uart_get_RX_FIFO_ERROR(UARTn uartn);

//return value:1(FIFO mode:Transmitter Shift Register,FIFO both empty)
//              (non-FIFO mode:Transmitter Holding Register ,Transmitter Shift Register both empty.)
//             0(not empty)
uint8_t uart_get_TX_ALL_EMPTY(UARTn uartn);

//return value:1(Transmit Holding Register Empty)
//             0(not empty)
uint8_t uart_get_TX_HOLD_EMPTY(UARTn uartn);

//return value:1(framing error)
//             0(no framing error)
uint8_t uart_get_FRAMING_ERROR(UARTn uartn);

//return value:1(parity error)
//             0(no parity error)
uint8_t uart_get_PARITY_ERROR(UARTn uartn);

//return value:1(overrun error)
//             0(no overrun error)
uint8_t uart_get_OVERRUN_ERROR(UARTn uartn);

//return value:1(data ready)
//             0(not data ready)
uint8_t uart_get_RX_DATA_READY(UARTn uartn);

//get the highest priority pending interrupt
//return value:0(modem status), 1(no interrupt pending), 2(THR empty)
//             4(received data available), 6(receiver line status)
uint8_t uart_get_Int_ID(UARTn uartn);

//enable and config uart FIFO
void uart_FIFO_config(UARTn uartn, RX_Trigger rx_trigger, TX_Trigger tx_trigger);

//disable uart FIFO
void uart_FIFO_disable(UARTn uartn);

//return value:1(Receive FIFO Full)
//             0(Receive FIFO not full )
uint8_t uart_get_RX_FIFO_FULL(UARTn uartn);

//return value:1(Receive FIFO is not empty)
//             0(Receive FIFO is empty )
uint8_t uart_get_RX_FIFO_EMPTY(UARTn uartn);

//return value:1(Transmit FIFO is not full)
//             0(Transmit FIFO is full)
uint8_t uart_get_TX_FIFO_FULL(UARTn uartn);

//return value:1(Transmit FIFO is empty)
//             0(Transmit FIFO is not empty)
uint8_t uart_get_TX_FIFO_EMPTY(UARTn uartn);

//return value:1(uart is busy)
//             0(uart is idle or inactive)
uint8_t get_uart_status(UARTn uartn);

//return value:number of data entries in transmit FIFO
uint32_t uart_get_TX_FIFO_ENTRY(UARTn uartn);

//return value:number of data entries in receive FIFO
uint32_t uart_get_RX_FIFO_ENTRY(UARTn uartn);

void uart_init(UARTn uartn, uint32_t baudrate, UART_Intn uart_int);

void uart_write_data(UARTn uartn, uint8_t write_data);

uint8_t uart_read_data(UARTn uartn);

#endif
