#include "gpio.h"
#include "gic.h"
#include "types.h"
//pin是引脚号；0为输入，1为输出
void gpio_set_direction(uint32_t pin, uint32_t direction) {
    uint32_t addr = GPIO_PORTA_DDR;//方向寄存器偏移量
    if(direction){
        //设置为输出
        WRITE_32(addr,READ_32(addr)|(1<<pin));//例如pin=3,则1<<3=1000,同时从原来的值中取或，确保其他位不变
    }
    else{
        //设置为输入
        WRITE_32(addr,READ_32(addr)&~(1<<pin));
    }
}

void gpio_write(uint32_t pin, uint32_t value) {//pin是引脚号；0为低电平，1为高电平
    uint32_t addr = GPIO_EXT_PORTA;//外部数据寄存器偏移量，尽管是仅仅读寄存器，但为了测试方便先可写
    if(value){
        //设置为高电平
        WRITE_32(addr,READ_32(addr)|(1<<pin));
    }
    else{
        //设置为低电平
        WRITE_32(addr,READ_32(addr)&~(1<<pin));
    }   
}

//输入输出分离架构，因此读取的是外部端口A寄存器
uint32_t gpio_read(uint32_t pin) {
    uint32_t addr = GPIO_EXT_PORTA;

    return (READ_32(addr)>>pin)&1;//返回的是pin引脚的值，而不是整个寄存器的值
}

extern int irq_count;

/* GPIO_0 ISR:简单地读取版本寄存器信息*/
void gpio_high_level_isr(int i){
    READ_32(GPIO_COMP_VERSION);//业务逻辑
    //终止中断状态
    //gpio_write(i, 0);                                     //方式1：直接拉低电平
    WRITE_32(GPIO_INTMASK, READ_32(GPIO_INTMASK)|(1<<i));   //方式2：屏蔽中断，而不是清除中断
}

void gpio_low_level_isr(int i){
    READ_32(GPIO_COMP_VERSION);//业务逻辑
    //终止中断状态
    //gpio_write(i, 1);                                     //方式1：直接拉高电平
    WRITE_32(GPIO_INTMASK, READ_32(GPIO_INTMASK)|(1<<i));   //方式2：屏蔽中断，而不是清除中断
}

void gpio_edge_isr(int i){
    READ_32(GPIO_ID_CODE);//业务逻辑
    //清除中断状态
    WRITE_32(GPIO_PORTA_EOI, READ_32(GPIO_PORTA_EOI)|(1<<i));  // 结束中断:0—无中断清除（默认）, 1—中断清除;
}

//有两种分发方式：
//1. 直接在中断服务函数中调用gpio_0_isr()和gpio_1_isr()，这样可以在中断服务函数中直接处理GPIO中断
//2. 调用gpio_isr()，然后在gpio_isr()中根据引脚号调用gpio_0_isr()和gpio_1_isr()，这样可以在gpio_isr()中统一处理GPIO中断
//目前采用第一种方式

// void gpio_isr(){
//     irq_count++;
// }

void gpio_isr(){//GPIO中断分发函数
    irq_count++;
    //读取中断状态寄存器
    uint32_t int_status = READ_32(GPIO_INTSTATUS);
    uint32_t int_mask = READ_32(GPIO_INTMASK);
    //用轮询的方式判断中断ISR(不考虑各引脚之间的优先级)
    for(int i = 0; i < 16; i++){//遍历16个引脚
        if(int_status & (1<<i) & ~int_mask){//如果第i个引脚有中断且中断未被屏蔽
            //判断中断类型
            if(READ_32(GPIO_INTTYPE_LEVEL)&(1<<i)){//如果是电平触发
                if(READ_32(GPIO_INT_POLARITY)&(1<<i)){//如果是高电平触发
                    gpio_high_level_isr(i);
                }
                else{//如果是低电平触发
                    gpio_low_level_isr(i);
                }
            }
            else{//如果是边沿触发
                gpio_edge_isr(i);
            }
        }
    }
}


void gpio_init(){//初始化GPIO 
    // 设置GPIO引脚0为输入模式
    WRITE_32(GPIO_PORTA_DDR, 0);         // 0表示输入模式

    // 配置中断触发类型
    WRITE_32(GPIO_INTTYPE_LEVEL, 0x01);  // 设置引脚0为电平触发，引脚1为边沿触发

    // 配置中断触发极性
    WRITE_32(GPIO_INT_POLARITY, 0x03);   // 高电平或上升沿触发中断

    // 使能中断引脚
    WRITE_32(GPIO_INTEN, 0x03);          // 使能引脚0、1的中断,也即0000 0011 0x03
}