#ifndef __GIC_H__
#define __GIC_H__

#include "common.h"

//----------------------------------------
//
//  Register Define
//
//---------------------------------------

//
// gic distributor interface
//
#define M1    (0x080-0x00c)>>2
#define M2    (0x100-0x080)>>2
#define M3    (0x180-0x100)>>2
#define M4    (0x200-0x180)>>2
#define M5    (0x280-0x200)>>2
#define M6    (0x300-0x280)>>2
#define M7    (0x380-0x300)>>2
#define M8    (0x400-0x380)>>2
#define M9    (0x7FC-0x400)>>2
#define M11   (0xBFC-0x800)>>2
#define M13   (0xD00-0xC00)>>2
#define M14   (0xD80-0xD04)>>2
#define M15   (0xF00-0XD80)>>2

#define M16   (0xFC-0x20)>>2

typedef struct
{
	__IO uint32_t ICDDCR;          //0x000
	__IO uint32_t ICDICTR;         //0x004
	__IO uint32_t ICDIIDR;         //0x008
	__IO uint32_t REVERSED_M1[M1]; //0x00c~0x07C
	__IO uint32_t ICDISR[M2];      //0x080~0x0FC
	__IO uint32_t ICDISER[M3];     //0x100~0x17C
	__IO uint32_t ICDICER[M4];     //0x180~0x1FC
	__IO uint32_t ICDISPR[M5];     //0x200~0x27C
	__IO uint32_t ICDICPR[M6];     //0x280~0x2FC
	__IO uint32_t ICDABR[M7];      //0x300~0x37C
	__IO uint32_t REVERSED_M8[M8]; //0x380~0x3FC
	__IO uint32_t ICDIPR[M9];      //0x400~0x7F8
	__IO uint32_t RESERVED_M10;    //0x7FC
	__IO uint32_t ICDIPTR[M11];    //0x800~0xBF8
	__IO uint32_t RESERVED_M12;     //0xBFC
	__IO uint32_t ICDICFR[M13];     //0xC00~0xCFC
	__IO uint32_t ICPPISR;          //0xD00
    __IO uint32_t ICSPISR[M14];     //0xD04~0xD1C
    __IO uint32_t RESERVED_M15[M15];//0xD80~0xEFC
	__IO uint32_t ICDSGIR;          //0xF00
}GIC_DIST_TypeDef;


//
// gic CPU interface
//
typedef struct
{
	__IO uint32_t ICCICR;           //0x00
	__IO uint32_t ICCPMR;           //0x04
	__IO uint32_t ICCBPR;           //0x08
	__IO uint32_t ICCIAR;           //0x0C
	__IO uint32_t ICCEOIR;          //0x10
	__IO uint32_t ICCRPR;           //0x14
	__IO uint32_t ICCHPIR;          //0x18
	__IO uint32_t ICCABPR;          //0x1C
	__IO uint32_t RESERVED_M16[M16];//0x20~0xF8
	__IO uint32_t ICCIIDR;          //0xFC
}GIC_CPU_TypeDef;

#define GIC_DIST_BASE_ADDR      (PERIPH_BASE_ADDR+0x1000)
#define GIC_CPU_BASE_ADDR       (PERIPH_BASE_ADDR+0x100)
#define GIC_DIST_REG            ((GIC_DIST_TypeDef *)     GIC_DIST_BASE_ADDR)
#define GIC_CPU_REG             ((GIC_CPU_TypeDef *)      GIC_CPU_BASE_ADDR)

//enable IRQ
void 
A9_irq_enable(void);

//disable IRQ
void 
A9_irq_disable(void);

//enable FIQ
void 
A9_fiq_enable(void);

//disable FIQ
void 
A9_fiq_disable(void);


//  Global enable of the Interrupt Distributor
void
enableGICDistributorInterface(void);

// Global disable of the Interrupt Distributor
void
disableGICDistributorInterface(void);

// Enables the interrupt source number ID
void 
enableIntID
(
    uint32_t ID
);

// Disables the interrupt source number ID
void 
disableIntID
(
    uint32_t ID
);

// Sets the priority of the specified ID
void
setIntPriority
(
    uint32_t ID,       //the interrupt source number ID
	uint32_t priority  //priority
);

// Enables the processor interface
// Must be done on each core separately
void 
enableGICProcessorInterface(void);

// Disables the processor interface
// Must be done on each core separately
void 
disableGICProcessorInterface(void);

// Sets the Priority mask register for the core run on
// The reset value masks ALL interrupts!
void 
setPriorityMask
(
    uint32_t priority    //mask value,range from 0~31
);

// Sets the Binary Point Register for the core run on
uint8_t 
setBinaryPoint
(
    uint32_t priority
);

// return sub priority fild
uint8_t
getBinaryPoint(void);

//  Returns the value of the Interrupt Acknowledge Register
uint32_t 
readIntAck(void);

// Writes ID to the End Of Interrupt register
void 
writeEOI
(
    uint32_t ID
);

//SGI Send a software generate interrupt
void 
sendSGI
(
    uint32_t ID, 
    uint32_t core_list, 
    uint32_t filter_list
);

//Find out how many interrupts are supported
uint32_t 
getIntNum(void);

#define MPCORE_TARGET_NONE      (0x0)    //none CPU
#define MPCORE_TARGET_CPU0      (0x1)    //CPU0
#define MPCORE_TARGET_CPU1      (0x2)    //CPU1
#define MPCORE_TARGET_CPU0_1    (0x3)    //CPU0 and CPU1
//sets the target CPUs of the specified ID
// For 'target' use one of the above defines
uint32_t 
setIntTarget
(
    uint32_t ID, 
    uint32_t target
);

//clear the pending status of all interrupts
void 
clearAllIntPending(void);

//clear special ID pending
void 
clearIDPending
(
    uint32_t ID    //the specified ID
);

//disable all interrupts forward to the CPU interfaces
void 
clearAllIntEnable(void);

//make specified ID interrupt none secure
void
makeIntNonSecure
(
    uint32_t ID
);

//make specified ID interrupt secure
void
makeIntSecure
(
    uint32_t ID
);

#define LEVEL_SENSITIVE    (0x0)    //level sensitive mode
#define EDGE_TRIGGER       (0x2)    //edge-triggered mode
//set the trigger mode of specified interrupt as edge-triggered or level-sensitive
// For 'trigger_mode' use one of the above defines
void 
setIntTriggerMode
(
    uint32_t ID, 
    uint32_t trigger_mode
);

#endif

