/*
 * SDRAM 驱动头文件
 */

#ifndef SDRAM_H
#define SDRAM_H

/* SDRAM 寄存器定义 */
#define SDRAM_BASE                          0xB0000000
#define SDRAM_SCONR                         (SDRAM_BASE + 0x00)     /* RW SDRAM 配置寄存器 */
#define SDRAM_STMG0R                        (SDRAM_BASE + 0x04)     /* RW SDRAM 时序寄存器 0 */
#define SDRAM_STMG1R                        (SDRAM_BASE + 0x08)     /* RW SDRAM 时序寄存器 1 */
#define SDRAM_SCTLR                         (SDRAM_BASE + 0x0C)     /* RW SDRAM 控制寄存器 */
#define SDRAM_SREFR                         (SDRAM_BASE + 0x10)     /* RW SDRAM 刷新寄存器 */
#define SDRAM_SCSLR0_LOW                    (SDRAM_BASE + 0x14)     /* RO 片选 0 地址寄存器 */
#define SDRAM_SMSKR0                        (SDRAM_BASE + 0x54)     /* RO 片选 0 地址掩码寄存器 */
#define SDRAM_EDAC_CTL                      (SDRAM_BASE + 0xC0)     /* WO EDAC 控制寄存器 */
#define SDRAM_ONEBIT_ERR_ADDR               (SDRAM_BASE + 0xC4)     /* RO 单错地址寄存器 */
#define SDRAM_ONEBIT_ERR_DATA               (SDRAM_BASE + 0xC8)     /* RO 单错数据寄存器 */
#define SDRAM_ONEBIT_ERR_EDAC               (SDRAM_BASE + 0xCC)     /* RO 单错校验码寄存器 */
#define SDRAM_TWOBITS_ERR_ADDR              (SDRAM_BASE + 0xD0)     /* RO 双错地址寄存器 */
#define SDRAM_TWOBITS_ERR_DATA              (SDRAM_BASE + 0xD4)     /* RO 双错数据寄存器 */
#define SDRAM_TWOBITS_ERR_EDAC              (SDRAM_BASE + 0xD8)     /* RO 双错校验码寄存器 */
#define SDRAM_RD_EDAC_ADDR_BYPASS           (SDRAM_BASE + 0xDC)     /* RW EDAC 读旁路地址寄存器 */
#define SDRAM_RD_EDAC_BYPASS                (SDRAM_BASE + 0xE0)     /* RW EDAC 读旁路校验码寄存器 */
#define SDRAM_RD_DATA_BYPASS                (SDRAM_BASE + 0xE4)     /* RW EDAC 读旁路数据寄存器 */
#define SDRAM_WR_EDAC_ADDR_BYPASS           (SDRAM_BASE + 0xE8)     /* RW EDAC 写旁路地址寄存器 */
#define SDRAM_WR_EDAC_DATA_BYPASS           (SDRAM_BASE + 0xEC)     /* RW EDAC 写旁路数据（校验码）寄存器 */
#define SDRAM_COMP_TYPE                     (SDRAM_BASE + 0xFC)     /* RO IP 类型寄存器 */
#define SDRAM_COMP_VERSION                  (SDRAM_BASE + 0xF8)     /* RO IP 版本寄存器 */
#define SDRAM_COMP_PARAMS_1                 (SDRAM_BASE + 0xF4)     /* RO IP 配置参数寄存器 1 */
#define SDRAM_COMP_PARAMS_2                 (SDRAM_BASE + 0xF0)     /* RO IP 配置参数寄存器 2 */

#endif /* SDRAM_H */
