.section ".text"
.global _start

_start:
    ldr r0, =flash_sdata
    ldr r1, =ram_sdata
    ldr r2, =data_size
    cmp r2, #0
    beq init_bss

copy:
    ldrb r4, [r0], #1
    strb r4, [r1], #1
    subs r2, r2, #1
    bne copy

init_bss:
    ldr r0, =sbss
    ldr r1, =ebss
    ldr r2, =bss_size

    cmp r2, #0
    beq init_stack
    mov r4, #0

zero:
    strb r4, [r0], #1
    subs r2, r2, #1
    bne zero

init_stack:
    ldr sp, =0x44000000
    bl main

stop: 
    b stop
