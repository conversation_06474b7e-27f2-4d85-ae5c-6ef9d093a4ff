#ifndef __BSP_EMIF_H__
#define __BSP_EMIF_H__

#include "types.h"

#define	EMIF_PROM_WIDTH_32				(32)
#define	EMIF_PROM_WIDTH_16				(16)
#define	EMIF_PROM_WIDTH_8				(8)

#define BSP_EMIF_REG_BASE_ADDR			(0xA00A0000)					/*the start address of emif，真实值为0xA00A0000*/

/*寄存器定义*/
#define WRMEM_CONFIG_REG				(0x0000)		//RAM空间配置
#define ORMEM_CONFIG_REG				(0x0004)		//ROM空间配置
#define IO_CONFIG_REG					(0x0008)		//IO空间配置

#define EDAC_CONFIG_REG					(0x000C)		//EDAC操作配置
#define IO_RDY_TIMEOUT_REG				(0x0018)		//IO空间使用Ready信号时，等待Ready信号到来的超时时间设置
#define EMIF_INT_CLEAR_REG				(0x001C)		//清中断状态寄存器
#define WR_WS_CFG_REG					(0x0020)		//RAM空间等待时序配置
#define OR_WS_CFG_REG					(0x0024)		//ROM空间等待时序配置
#define IO_WS_CFG_REG					(0x0028)		//IO空间等待时序配置

#define CERR_ADDR_REG					(0x0030)		//最近发生单错的访问字地址
#define CERR_DATA_REG					(0x0034)		//最近发生单错的访问字数据
#define CERR_EDAC_REG					(0x0038)		//最近发生单错的访问字EDAC码
#define DERR_ADDR_REG					(0x0040)		//最近发生双错的访问字地址
#define DERR_DATA_REG					(0x0044)		//最近发生双错的访问字数据
#define DERR_EDAC_REG					(0x0048)		//最近发生双错的访问字EDAC码

/* BSP设置 */
#define	BSP_ENABLE			             0x1				//使能/禁止
#define	BSP_DISABLE						 0x0				//使能/禁止
#define BSP_EMIF_DOUBLE_ERR_EN			(BSP_ENABLE)		//发生双错时是否通过AHB总线HRESP信号报告数据错误中断，BSP_ENABLE:使能;BSP_DISABLE:禁止
#define BSP_EMIF_ROMEDAC_EN				(BSP_ENABLE)		//ROM区EDAC使能 BSP_ENABLE:使能;BSP_DISABLE:禁止
#define BSP_EMIF_ROM_OR_WRITE_BACK_EN	(BSP_DISABLE)		//ROM空间纠错写回使能位 BSP_ENABLE:使能;BSP_DISABLE:禁止
#define SYSTRUE					        (0xeb90146f)		//系统状态，成功
#define SYSFALSE				        (0x0)				//系统状态，失败
#define BSP_EMIF_SEU_IRQID			    (81)				//单错中断
#define BSP_EMIF_WRITE_TO_FORBIDEN_AREA_ERR_IRQID	(82)	//禁止区写入错误中断
#define BSP_EMIF_IO_RDY_BUS_TIMEOUT_ERR_IRQID		(83)	//IO空间超时错误中断
#define BSP_EMIF_DOUBLE_ERR_INT		    (100)				//双错中断
#define GFS_EMIF_SRAM_AVAILABLE			(1)					//EMIF SRAM可用

/* 数据位宽 */	
#define BSP_EMIF_DATA8						(0x0)			//8位数据宽度
#define BSP_EMIF_DATA16						(0x1)			//16位数据宽度
#define BSP_EMIF_DATA32						(0x3)			//32位数据宽度

/*emif------------------------------ram*/
#define BSP_EMIF_RAM_512K					(0x0)
#define BSP_EMIF_RAM_1M						(0x7)
#define BSP_EMIF_RAM_2M						(0x8)
#define BSP_EMIF_RAM_4M						(0x9)
#define BSP_EMIF_RAM_8M						(0xa)
#define BSP_EMIF_RAM_16M					(0xb)
#define BSP_EMIF_RAM_32M					(0xc)
#define BSP_EMIF_RAM_64M					(0xf)

/*emif------------------------------rom*/
#define BSP_EMIF_ROM_8K						(0x0)
#define BSP_EMIF_ROM_16K					(0x1)
#define BSP_EMIF_ROM_32K					(0x2)
#define BSP_EMIF_ROM_64K					(0x3)
#define BSP_EMIF_ROM_128K					(0x4)
#define BSP_EMIF_ROM_256K					(0x5)
#define BSP_EMIF_ROM_512K					(0x6)
#define BSP_EMIF_ROM_1M						(0x7)	
#define BSP_EMIF_ROM_2M						(0x8)
#define BSP_EMIF_ROM_4M						(0x9)
#define BSP_EMIF_ROM_8M						(0xa)
#define BSP_EMIF_ROM_16M					(0xb)
#define BSP_EMIF_ROM_32M					(0xc)
#define BSP_EMIF_ROM_64M					(0xf)

/*========RAM空间配置==============*/
/*Emif空间SRAM大小*/
#define BSP_EMIF_RAM_SIZE				(1*1024*1024)

/*访问有效时长ws配置，当量为AHB总线时钟周期,范围为0~255*/
#define BSP_EMIF_RAM_WRMEM_WS			(0xA)

/*RAM空间容量*/
#define BSP_EMIF_RAM_WRMEM_BS			(BSP_EMIF_RAM_2M)

/*读修改写使能 BSP_ENABLE:使能;BSP_DISABLE:禁止*/
#define BSP_EMIF_RAM_WRMEM_RMW			(BSP_ENABLE)

/*RAM空间外接数据位宽*/
#define BSP_EMIF_RAM_WRMEM_WT			(BSP_EMIF_DATA32)

/*EDAC使能 BSP_ENABLE:使能;BSP_DISABLE:禁止*/
#define BSP_EMIF_RAMEDAC_EN				(BSP_ENABLE)

/*RAM空间纠错写回使能位 BSP_ENABLE:使能;BSP_DISABLE:禁止*/
#define BSP_EMIF_RAM_WR_WRITE_BACK_EN	(BSP_DISABLE)

/*等待时序配置*/

/*访问有效时长 (hold 1)配置，范围为0~255，读写操作等待时长=配置值 + 1*/
#define BSP_EMIF_RAM_WRMEM_WS_H1		(0x1)

/*访问有效时长 (hold 2)配置，范围为0~255，读写操作等待时长=配置值 + 1*/
#define BSP_EMIF_RAM_WRMEM_WS_H2		(0x0)

/*访问有效时长 (setup 1)配置，范围为0~255，读写操作等待时长=配置值 + 1*/
#define BSP_EMIF_RAM_WRMEM_WS_S1		(0x2)

/*访问有效时长 (setup 2)配置，范围为0~255，读写操作等待时长=配置值 + 1*/
#define BSP_EMIF_RAM_WRMEM_WS_S2		(0x2)

/*========ROM空间配置==============*/

/*访问有效时长ws配置，当量为AHB总线时钟周期,范围为0~255*/
#define BSP_EMIF_ROM_ORMEM_WS			(0x28)

/*ROM空间容量*/
#define BSP_EMIF_ROM_ORMEM_BS			(BSP_EMIF_ROM_64M)

/*读修改写使能 BSP_ENABLE:使能;BSP_DISABLE:禁止*/
#define BSP_EMIF_ROM_ORMEM_RMW			(BSP_DISABLE)

/*ROM空间外接数据位宽*/
#define BSP_EMIF_ROM_ORMEM_WT			(BSP_EMIF_DATA8)

/*写使能 BSP_ENABLE:使能;BSP_DISABLE:禁止*/
#define BSP_EMIF_ROM_ORMEM_WEN			(BSP_ENABLE)

/*EDAC使能 BSP_ENABLE:使能;BSP_DISABLE:禁止*/
#define BSP_EMIF_ROMEDAC_EN				(BSP_ENABLE)

/*ROM空间纠错写回使能位 BSP_ENABLE:使能;BSP_DISABLE:禁止*/
#define BSP_EMIF_ROM_OR_WRITE_BACK_EN	(BSP_DISABLE)


/*等待时序配置*/

/*访问有效时长 (hold 1)配置，范围为0~255，读写操作等待时长=配置值 + 1*/
#define BSP_EMIF_ROM_ORMEM_WS_H1		(0x5)

/*访问有效时长 (hold 2)配置，范围为0~255，读写操作等待时长=配置值 + 1*/
#define BSP_EMIF_ROM_ORMEM_WS_H2		(0xB)

/*访问有效时长 (setup 1)配置，范围为0~255，读写操作等待时长=配置值 + 1*/
#define BSP_EMIF_ROM_ORMEM_WS_S1		(0x1)

/*访问有效时长 (setup 2)配置，范围为0~255，读写操作等待时长=配置值 + 1*/
#define BSP_EMIF_ROM_ORMEM_WS_S2		(0x1)

/*========IO空间配置==============*/
/*访问有效时长ws配置，当量为AHB总线时钟周期,范围为0~255*/
#define BSP_EMIF_IO_ws					(0x80)

/*IO空间外接数据位宽*/
#define BSP_EMIF_IO_wt					(BSP_EMIF_DATA32)

/*IO空间访问完成使能,
0：使用等待周期方式
1：使用访问完成指示方式
*/
#define BSP_EMIF_IO_rdy					(0)

/*等待时序配置*/
/*访问有效时长 (hold 1)配置，范围为0~255，读写操作等待时长=配置值 + 1*/
#define BSP_EMIF_IO_IOMEM_WS_H1			(0x8)

/*访问有效时长 (hold 2)配置，范围为0~255，读写操作等待时长=配置值 + 1*/
#define BSP_EMIF_IO_IOMEM_WS_H2			(0x8)

/*访问有效时长 (setup 1)配置，范围为0~255，读写操作等待时长=配置值 + 1*/
#define BSP_EMIF_IO_IOMEM_WS_S1			(0x8)

/*访问有效时长 (setup 2)配置，范围为0~255，读写操作等待时长=配置值 + 1*/
#define BSP_EMIF_IO_IOMEM_WS_S2			(0x8)

/*EMIF RAM空间寄存器配置 */
#define BSP_EMIF_RAM_WRMEM_CONFIG_REG  ( (BSP_EMIF_RAM_WRMEM_WT << 14)|			\
											(BSP_EMIF_RAM_WRMEM_RMW << 12)|		\
											(BSP_EMIF_RAM_WRMEM_BS << 8)|		\
												BSP_EMIF_RAM_WRMEM_WS)

/*EMIF RAM 等待时序寄存器配置*/
#define BSP_EMIF_RAM_WR_WS_CFG_REG		( (BSP_EMIF_RAM_WRMEM_WS_S1 << 24)|		\
										(BSP_EMIF_RAM_WRMEM_WS_S2 << 16)|		\
										(BSP_EMIF_RAM_WRMEM_WS_H2 << 8)|		\
												BSP_EMIF_RAM_WRMEM_WS_H1)

/* ROM空间寄存器配置 */
#define BSP_EMIF_ROM_ORMEM_config_reg  	( (BSP_EMIF_ROM_ORMEM_WEN<<16)|			\
										(BSP_EMIF_ROM_ORMEM_WT << 14)|			\
										(BSP_EMIF_ROM_ORMEM_RMW << 12)|			\
										(BSP_EMIF_ROM_ORMEM_BS << 8)|			\
										BSP_EMIF_ROM_ORMEM_WS)

/*ROM 等待时序寄存器配置*/
#define BSP_EMIF_ROM_OR_WS_CFG_REG		( (BSP_EMIF_ROM_ORMEM_WS_S1 << 24)|		\
										(BSP_EMIF_ROM_ORMEM_WS_S2 << 16)|		\
										(BSP_EMIF_ROM_ORMEM_WS_H2 << 8)|		\
										BSP_EMIF_ROM_ORMEM_WS_H1)


/* IO空间寄存器配置 */
#define BSP_EMIF_IO_config_reg			( (BSP_EMIF_IO_rdy << 10)|		\
											(BSP_EMIF_IO_wt << 8)|		\
												BSP_EMIF_IO_ws)
		
/*IO 等待时序寄存器配置*/
#define BSP_EMIF_IO_WS_CFG_REG 			( (BSP_EMIF_IO_IOMEM_WS_S1 << 24)|		\
											(BSP_EMIF_IO_IOMEM_WS_S2 << 16)|	\
											(BSP_EMIF_IO_IOMEM_WS_H2 << 8)|		\
												BSP_EMIF_IO_IOMEM_WS_H1)

/*最近发生单错的访问字地址*/
extern U32 BSP_EMIF_SingleErr_GetAddr(void);

/*最近发生双错的访问字地址*/
extern U32 BSP_EMIF_MultiErr_GetAddr(void);

/*使能/禁止SRAM区EDAC*/
extern void Bsp_SetEmifRamEdac(U32 set);

/*读旁路使能/禁止*/
extern void Bsp_EmifSetRamEdacReadByPass(U32 set);

/*写旁路使能/禁止*/
extern void Bsp_EmifSetRamEdacWriteByPass(U32 set);

/*使能/禁止ROM区EDAC*/
extern void Bsp_SetRomEdac(U32 set);

extern U32 Bsp_GetRomEdac(void);

/*EMIF错误中断清零*/
extern I32 Bsp_EmifClearIntStatus(U32 isrID);

/*设置ROM区位宽*/
extern void Bsp_SetRomWidth(U32 width);

extern U32 Bsp_GetRomWidth(void);

extern void Bsp_EmifInit(void);

#endif //__BSP_EMIF_H__


