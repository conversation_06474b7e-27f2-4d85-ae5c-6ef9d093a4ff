/*
* Copyright (C) 2019 by Xi'an AeroSpace HuaXun Technology Co.,Ltd. All Rights Reserved
* MODULE:   GIC
* FILENAME: GIC_IRQ.C
* Author   :<EMAIL>
* Date:	   2019.05.05
*/

/***************************************************************************************
*   INCLUDE
***************************************************************************************/
#include "gic_irq.h"
#include "types.h"


/***************************************************************************************
*   FUNCTION
***************************************************************************************/
void GIC_IRQ_Disable_Dist(void)
{
	w32(GIC_DIST_CTRL,GIC_DIST_DISABLE);
}

void GIC_IRQ_Disable_Cpu(void)
{
	w32(GIC_CPU_CTRL,GIC_CPU_DISABLE);
}


void GIC_IRQ_Disable_Interrupts(void)
{
	GIC_IRQ_Disable_Dist();
	GIC_IRQ_Disable_Cpu();
}

void GIC_IRQ_Set_Mask(unsigned int irq)
{
	unsigned int offs = (irq / 32) * 4;
	unsigned int mask = 1 << (irq % 32);

	w32(GIC_DIST_ENABLE_CLEAR + offs,mask);
}

void GIC_IRQ_Clear_Pending(unsigned int irq)
{
	unsigned int offs = (irq / 32) * 4;
	unsigned int mask = 1 << (irq % 32);

	w32(GIC_DIST_PENDING_CLEAR + offs,mask);
}

void GIC_IRQ_Clear_Active(unsigned int irq)
{
	unsigned int offs = (irq / 32) * 4;
	unsigned int mask = 1 << (irq % 32);

	w32(GIC_DIST_ACTIVE_CLEAR + offs,mask);
}

void GIC_IRQ_Set_Trigger(unsigned int irq, unsigned int mode)
{
	unsigned int offs = (irq / 16) * 4;
	unsigned int pos = (irq % 16) * 2;
	unsigned int reg = r32(GIC_DIST_CONFIG + offs);

	if(mode == IRQ_TRIG_LEVEL)
		reg &= ~(0x3 << pos); //set 00b, level
	else
		reg |= (0x2 << pos);  //set 10b, edge

	w32(GIC_DIST_CONFIG + offs,reg);
}


void GIC_IRQ_Set_Target(unsigned int irq, unsigned int cpu)
{
	unsigned int offs = (irq / 4) * 4;
	unsigned int pos = (irq % 4) * 8;
	unsigned int reg;

	reg = r32(GIC_DIST_TARGET + offs);
	reg &= ~(0xff << pos);
	reg |= (1 << cpu) << pos;

	w32(GIC_DIST_TARGET + offs,reg);
}

void GIC_IRQ_Set_Priority(unsigned int irq, unsigned int prio)
{
	unsigned int offs = (irq / 4) * 4;
	unsigned int pos = (irq % 4) * 8;
	unsigned int reg;

	reg = r32(GIC_DIST_PRI + offs);
	reg &= ~(0xff << pos);
	reg |= (0xff & prio) << pos;

	w32(GIC_DIST_PRI + offs,reg);
}


void GIC_IRQ_Set_Primask(unsigned int mask)
{
	w32(GIC_CPU_PRIMASK,mask);
}


void GIC_IRQ_Enable_Cpu(void)
{
	w32(GIC_CPU_CTRL,GIC_CPU_ENABLE);
}

void GIC_IRQ_Enable_Dist(void)
{
	w32(GIC_DIST_CTRL,GIC_DIST_ENABLE);
}


void GIC_IRQ_Enable_Interrupts(void)
{
	GIC_IRQ_Enable_Cpu();
	GIC_IRQ_Set_Primask(0xff);
	GIC_IRQ_Enable_Dist();
}


void GIC_IRQ_unmask(unsigned int irq)
{
	unsigned int offs = (irq / 32) * 4;
	unsigned int mask = 1 << (irq % 32);

	w32(GIC_DIST_ENABLE_SET + offs,mask);
}


unsigned int GIC_IRQ_GetINTACK(void)
{
	return r32(GIC_CPU_INTACK);
}

void GIC_IRQ_SetEOI(unsigned int irq)
{
      w32(GIC_CPU_EOI,irq&0x3ff);
}



void GIC_IRQ_Init(unsigned int Irq)
{
	print2("Begin Init GIC IRQ Module");
  
	GIC_IRQ_Disable_Interrupts();
	
	GIC_IRQ_Set_Mask(Irq);
	
	GIC_IRQ_Clear_Pending(Irq);

	GIC_IRQ_Clear_Active(Irq);

	GIC_IRQ_Set_Trigger(Irq, IRQ_TRIG_EDGE);

	GIC_IRQ_Set_Target(Irq, IRQ_TGT_CPU0);

	GIC_IRQ_Set_Priority(Irq, 0);

	GIC_IRQ_unmask(Irq);

	GIC_IRQ_Enable_Interrupts(); 
	
	print2("End GIC Module Init");
}

