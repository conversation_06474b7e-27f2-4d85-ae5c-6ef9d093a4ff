#include "secure_util.h"
#include "gic.h"
#include "types.h"

void set_vbar_ns(void) {
    uint32_t base = 0x10000000;
    __asm__ volatile ("mrc p15, 0, %0, c12, c0, 0" : "=r" (base));
}

bool is_ns(void) {
    uint32_t scr;

    __asm__ volatile ("mrc p15, 0, %0, c1, c1, 0" : "=r" (scr));
    if ((scr & 0x1) != 0) {
        return true;
    } else {return false;}
}

void switch_to_ns(void) {
    __asm__ volatile ("mrc p15, 0, r0, c1, c1, 0");
    __asm__ volatile ("orr r0, r0, #0b1");
    __asm__ volatile ("mcr p15, 0, r0, c1, c1, 0");
}

void switch_to_s(void) {
    __asm__ volatile ("smc #0");
    int ns_flag = is_ns();
}
