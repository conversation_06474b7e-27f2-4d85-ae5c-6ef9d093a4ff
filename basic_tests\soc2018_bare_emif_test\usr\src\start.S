#define STACK_TOP       (0x107f0000)

    .macro ARM_SECTION_ENTRY base,tex,ap,d,c,b
        .word (\base << 20) | (\tex << 12)| (\ap << 10) | (\d << 5) | (0<<4) | (\c << 3) | (\b << 2) | (1<<1)
    .endm
    
    .section .vectors, "ax", %progbits
    .align 5 @ 对齐到 32 Byte

vectors:
    b       Reset_Handler             @ Reset 异常入口
    b       Undefined_Handler         @ 未定义指令异常
    b       SWI_Handler               @ 软件中断异常
    b       Prefetch_Abort_Handler    @ 预取中止异常
    b       Data_Abort_Handler        @ 数据中止异常
    b       Reserved_Handler          @ 保留异常
    b       IRQ_Handler               @ IRQ 中断异常
    b       FIQ_Handler               @ FIQ 中断异常

    .section .text
    .global Reset_Handler
    .extern main, __exception_vector_start, __bss_start, __bss_end, gic_handle_irq
    .align 4

Reset_Handler:
    /* 保存启动参数 */
    bl      save_boot_params

    /* CPSR：处理器运行模式设置与中断禁用 */ 
    mrs     r0, cpsr            @ 读取 CPSR 至 R0
    and     r1, r0, #0x1f       @ 读取 CPSR 当前 Mode 位到 R1
    teq     r1, #0x1a           @ 检测是否为 HYP 模式
    bicne   r0, r0, #0x1f       @ （非 HYP 模式）清零 Mode 位
    orrne   r0, r0, #0x13       @ （非 HYP 模式）设置为 SVC 模式
    orr     r0, r0, #0xc0       @ 禁用 FIQ 与 IRQ
    msr     cpsr, r0            @ 从 R0 写回 CPSR

    bl      exception_vector_init   @ 异常向量表初始化
    bl      cpu_init_cp15           @ CP15 寄存器组初始化
    bl      vfp_init                @ VFP 单元初始化
    b       lower_init              @ 板级初始化、用户函数执行

cpu_init_cp15:
    /* 清理 L1 I/D Cache */
    mov     r0, #0
    mcr     p15, 0, r0, c8, c7, 0   @ 触发 TLBIALL 指令，失效缓存条目
    mcr     p15, 0, r0, c7, c5, 0   @ 触发 ICIALLU 指令
    mcr     p15, 0, r0, c7, c5, 6   @ 触发 BPIALL 指令
    dsb
    isb

    /* 禁用 MMU 及相关标志位清零 */
    mrc     p15, 0, r0, c1, c0, 0   @ 读取 SCTLR
    bic     r0, r0, #0x00000007     @ 清零 C A M 标志位
    orr     r0, r0, #0x00000002     @ A 标志位置 1，启用对其故障检查

    /* 程序流预测设置 */
    orr     r0, r0, #0x00000800     @ Z 标志位置 1，启用指令预取

    /* 指令缓存设置 */
    orr     r0, r0, #0x00001000     @ I 标志位置 1，启用指令缓存

    mcr     p15, 0, r0, c1, c0, 0
    mov     pc, lr

vfp_init:
    /* 设置非安全状态的 CP10 和 CP11 为全访问 */
    mcr     p15, 0, r0, c1, c1, 2   @ 读取 NSACR
    orr     r0, r0, #(0x3 << 10)
    mrc     p15, 0, r0, c1, c1, 2
    
    /* 设置 CP10 和 CP11 为全访问 */
    mrc     p15, 0, r0, c1, c0, 2   @ 读取 CPACR
    orr     r0, r0, #(0xF << 20)
    mcr     p15, 0, r0, c1, c0, 2
    isb

    /* 设置 FPEXC 寄存器的 EN 位 */
    vmrs    r0, fpexc
    orr     r0, r0, #(1 << 30)
    vmsr    fpexc, r0

    mov     pc, lr

exception_vector_init:
    /* CP15 系统控制寄存器中 V 标志置 0 */
    mrc     p15, 0, r0, c1, c0, 0
    bic     r0, r0, #(1 << 13)
    mcr     p15, 0, r0, c1, c0, 0

    /* 将 VBAR 寄存器设置为异常向量表所在地址（0x10000000） */
    ldr     r0, =__exception_vector_start
    mcr     p15, 0, r0, c12, c0, 0

    mov     pc, lr

lower_init:
    ldr     r0, =STACK_TOP
    /* SVC 模式栈设置 */
    msr     cpsr_c, #0x13|0xc0
    mov     sp, r0
    sub     r0, r0, #0x4000

    /* IRQ 模式栈设置 */
    msr     cpsr_c, #0x12|0xc0
    mov     sp, R0
    sub     r0, r0, #0x4000

    /* SYS 模式栈设置 */
    msr     cpsr_c, #0x1f|0xc0
    mov     sp, r0
    sub     r0, r0, #0x4000
    
    /* MMU 一级翻译表加载及配置 */
    ldr     r3, _mmu_table_base
    mrc     p15, 0, r0, c2, c0, 2   @ 读取 TTBCR 
    bic     r0, r0, #0x37           @ 清零 N PD0 PD1 标志位
    orr     r0, r0, #0x20           @ 失效 TTBR1
    mcr     p15, 0, r0, c2, c0, 2

    orr     r0, r3, #0x8            @ 设置 RGN 位
    mcr     p15, 0, r0, c2, c0, 0   @ 写回 TTBR0

    mov     r0, #0
    mcr     p15, 0, r0, c8, c7, 0   @ 失效缓存条目

    mov     r0, #-1                 @ 开启所有内存域的可读可写权限
    mcr     p15, 0, r0, c3, c0, 0   @ 写回 DACR

    dsb
    isb

    /* 使能 MMU */
    mrc     p15, 0, r0, c1, c0, 0           @ 读取 SCTLR 
    orr     r0, r0, #((1 << 2)|(1 << 0))    @ MMU 与 Dcache 使能
    mcr     p15, 0, r0, c1, c0, 0
    
    /* BSS 段初始化 */
    mov     r1, #0
    ldr     r2, =__bss_start
    ldr     r3, =__bss_end
    cmp     r3, r2
    beq     start_main
init_loop:
    str     r1, [r2], #4
    cmp     r3, r2
    bne     init_loop

    /* 跳转至用户函数 */
start_main:
    bl      main
    
    /* 无限循环 */
    b       .

IRQ_Handler:
    sub     lr, lr, #4          @ 调整返回地址
    push    {lr}                @ 保存返回地址

    mrs     lr, spsr           
    push    {lr}                @ 保存获取到 LR 的 SPSR 寄存器值
    
    cps     #0x13               @ 切换到 SVC 模式（其实目前已经在 SVC 模式）
    push    {r0-r3}             @ 保存通用寄存器到 SVC 栈

    bl      gic_handle_irq      @ 调用中断处理函数

    pop     {r0-r3}             @ 恢复通用寄存器
    cps     #0x12               @ 切换到 IRQ 模式
    pop     {lr}
    msr     spsr, lr            @ 将 SPSR 写回寄存器
    pop     {lr}                @ 恢复返回地址

    cpsie   i                   @ 开中断
    dsb
    isb

    movs    pc, lr              @ 从中断返回

Undefined_Handler:
    b       Reset_Handler

SWI_Handler:
    b       Reset_Handler

Prefetch_Abort_Handler:
    b       Reset_Handler

Data_Abort_Handler:
    b       Reset_Handler

Reserved_Handler:
    b       Reset_Handler

FIQ_Handler:
    b       Reset_Handler

_mmu_table_base:
    .word mmu_table

    .global save_boot_params
    .type save_boot_params, STT_FUNC
    .weak save_boot_params
save_boot_params:
    bx      lr

    .section .mmudata, "a"
    .align 14
    .global mmu_table
@mmu_table:
    @/* 1) VA 0x0000_0000–0x3FFF_FFFF → PA 0x0000_0000–0x3FFF_FFFF */
    @.set _base, 0x000
    @.rept 0x400             /* 0x3FF – 0x000 + 1 = 0x400 sections */
    @    ARM_SECTION_ENTRY _base, 3, 0, 0, 0, 0
    @    .set _base, _base+1
    @.endr
@
    @/* 2) VA 0x4000_0000–0x4FFF_FFFF → PA 0x9000_0000–0x9FFF_FFFF (256 MB) */
    @.set _base, 0x900       /* 0x900 * 1 MB = 0x9000_0000 */
    @.rept 0x100             /* 0x4FF – 0x400 + 1 = 0x100 sections */
    @    ARM_SECTION_ENTRY _base, 3, 0, 0, 0, 0
    @    .set _base, _base+1
    @.endr
@
    @/* 3) VA 0x5000_0000–0x8FFF_FFFF → PA 0x5000_0000–0x8FFF_FFFF */
    @.set _base, 0x500       /* 0x500 * 1 MB = 0x5000_0000 */
    @.rept 0x400             /* 0x8FF – 0x500 + 1 = 0x400 sections */
    @    ARM_SECTION_ENTRY _base, 3, 0, 0, 0, 0
    @    .set _base, _base+1
    @.endr
@
    @/* 4) VA 0x9000_0000–0x9FFF_FFFF → PA 0x4000_0000–0x4FFF_FFFF (256 MB) */
    @.set _base, 0x400       /* 0x400 * 1 MB = 0x4000_0000 */
    @.rept 0x100             /* 0x9FF – 0x900 + 1 = 0x100 sections */
    @    ARM_SECTION_ENTRY _base, 3, 0, 0, 0, 0
    @    .set _base, _base+1
    @.endr
@
    @/* 5) VA 0xA000_0000–0xFFFF_FFFF → PA 0xA000_0000–0xFFFF_FFFF */
    @.set _base, 0xA00       /* 0xA00 * 1 MB = 0xA000_0000 */
    @.rept 0x600             /* 0xFFF – 0xA00 + 1 = 0x600 sections */
    @    ARM_SECTION_ENTRY _base, 3, 0, 0, 0, 0
    @    .set _base, _base+1
    @.endr
mmu_table:
    .set _base,0
    .rept 0x40          @ RO_1
    ARM_SECTION_ENTRY _base,0,3,0,1,0 @ Outer and Inner Write-through, no Write-Allocate
    .set _base,_base+1
    .endr
    .rept 0x80 - 0x40   @ RO_2
    ARM_SECTION_ENTRY _base,0,3,0,1,0 @ Outer and Inner Write-through, no Write-Allocate
    .set _base,_base+1
    .endr
    .rept 0x100 - 0x80  @ IO
    ARM_SECTION_ENTRY _base,0,3,0,0,0 @ strong order
    .set _base,_base+1
    .endr
    .rept 0x140 - 0x100 @ WR_1 
    ARM_SECTION_ENTRY _base,1,3,0,1,1 @ Outer and Inner Write-back, Write-Allocate
    .set _base,_base+1
    .endr
    .rept 0x180 - 0x140 @ WR_2 
    ARM_SECTION_ENTRY _base,1,3,0,1,1 @ Outer and Inner Write-back, Write-Allocate
    .set _base,_base+1
    .endr
    .rept 0x1c0 - 0x180 @ WR_3 
    ARM_SECTION_ENTRY _base,1,3,0,1,1 @ Outer and Inner Write-back, Write-Allocate
    .set _base,_base+1
    .endr
    .rept 0x200 - 0x1c0 @ WR_4 
    ARM_SECTION_ENTRY _base,1,3,0,1,1 @ Outer and Inner Write-back, Write-Allocate
    .set _base,_base+1
    .endr
    .rept 0x2d0 - 0x200 @ SPW 1553B NANDFLASH_REG
    ARM_SECTION_ENTRY _base,0,3,0,0,0 @ strong order
    .set _base,_base+1
    .endr
    .rept 0x300 - 0x2d0 @ NANDFLASH_RAM
    ARM_SECTION_ENTRY _base,0,3,0,0,0 @ strong order
    .set _base,_base+1
    .endr
    .rept 0x400 - 0x300 @ SPW 1553B NANDFALSH_REG
    ARM_SECTION_ENTRY _base,0,3,0,0,0 @ strong order
    .set _base,_base+1
    .endr
    .rept 0x402 - 0x400 @ SRAM
    ARM_SECTION_ENTRY _base,1,3,0,1,1 @ Outer and Inner Write-through, no Write-Allocate
    .set _base,_base+1
    .endr
    .rept 0x404 - 0x402 @ SRAM
    ARM_SECTION_ENTRY _base,0,3,0,0,0 @ Outer and Inner Write-through, no Write-Allocate
    .set _base,_base+1
    .endr
    .rept 0xC00 - 0x404
    ARM_SECTION_ENTRY _base,0,3,0,0,0 @ strong order
    .set _base,_base+1
    .endr
    .rept 0x1000 - 0xc00
    ARM_SECTION_ENTRY _base,0,3,0,0,0 @ strong order
    .set _base,_base+1
    .endr