#include "types.h"
#include "irq_gic.h"

static int_handle_func_ptr irq_handle_vec[MAX_IRQS];

/*
 *Get the irq number by read bits of [9:0] in GICC_IAR register.获得中断号
 */
unsigned int gic_get_irq_number(void)
{
	unsigned int hwirq;
	hwirq = (r32(GIC_CPU_BASE + GIC_CPU_INTACK)) & 0x3ff;
	return hwirq;
}

/*
 * Acknowledge, disable and enable interrupts
 */
void gic_enable_irq(unsigned int irq)
{
	unsigned int mask = 1 << (irq % 32);
	w32(GIC_DIST_BASE + GIC_DIST_ENABLE_SET +  (irq / 32) * 4, mask);
}

void gic_disable_irq(unsigned int irq)
{
	unsigned int mask = 1 << (irq % 32);
	w32(GIC_DIST_BASE + GIC_DIST_ENABLE_CLEAR + (irq / 32) * 4, mask);
}

void gic_eoi_irq(unsigned int irq)
{
	w32(GIC_CPU_BASE + GIC_CPU_EOI, irq);
}

void gic_set_type(unsigned int irq, unsigned int type)
{
	if(irq < 16)
		return;
	if( (type != IRQ_TYPE_LEVEL_HIGH) && (type != IRQ_TYPE_EDGE_RISING) )
		return;

	gic_configure_irq(irq, type);
}

void gic_send_sgi(unsigned int cpu_id, unsigned int irq)
{
	cpu_id = 1 << cpu_id;
	w32(GIC_DIST_BASE + GIC_DIST_SOFTINT, (cpu_id << 16) | irq);
}

/*
 * Enable and disable global interrupt 
 */
void gic_global_enable(void)
{
	w32(GIC_DIST_BASE + GIC_DIST_CTRL, 0x3);
	//w32(GIC_CPU_BASE + GIC_CPU_CTRL, 0xf);
}
//全局disable ICDDCR和ICCICR
void gic_global_disable(void)
{ 
	u32 i=0;
	w32(GIC_DIST_BASE + GIC_DIST_CTRL, 0);
	w32(GIC_CPU_BASE + GIC_CPU_CTRL, 0);
	//禁止所有中断set enable
    for (i = 32; i < MAX_IRQS; i += 32)
	w32(GIC_DIST_BASE + GIC_DIST_ENABLE_CLEAR + i / 8, 0xffffffff);
}

void gic_dist_config(unsigned int gic_irqs)
{
	unsigned int i;

 /*中断安全性设置，i为中断号，端口0-31（中断号32-63）为Secure中断，初始化后默认为0 Secure；64-127为Non-Secure中断，相应位置1。
 若将SGI配置成Secure则SGI会触发FIQ，如果同时使用FIQ和SGI，需将SGI配置成Non-secure。
*/
    w32(GIC_DIST_BASE + GIC_DIST_Security, 0xffffffff);//SGI和PPI安全性为Non-secure
    //w32(GIC_DIST_BASE + GIC_DIST_Security, 0x0);//SGI和PPI安全性为Secure

   for (i = 64; i <= gic_irqs; i += 32)//加上等号才能设置最后一位
	{
		w32(GIC_DIST_BASE + GIC_DIST_Security + i/8, 0xffffffff);//Non-secure中断设置
       // mprintf("interrupt security register+%d is 0x%x\n", i/8,r32(GIC_DIST_BASE + GIC_DIST_Security + i/8));
	}	

   //  触发特性设置.0x55555555电平触发 高有效；0xffffffff则为上升沿触发
	for (i = 32; i < gic_irqs; i += 16)
		w32(GIC_DIST_BASE + GIC_DIST_CONFIG + i / 4, 0xffffffff);
		//w32(GIC_DIST_BASE + GIC_DIST_CONFIG + i / 4, 0x55555555);

//Set priority on all global interrupts.i就是对应中断号，从32开始。优先级分配如下：
	for (i = 32; i < 64; i += 4)
	{
	   w32(GIC_DIST_BASE + GIC_DIST_PRI + i , 0x10101010);//Secure中断优先级分配0x10，根据中断号判优
	  // mprintf("interrupt priority register+%d is 0x%x\n", i,r32(GIC_DIST_BASE + GIC_DIST_PRI + i ));
	} 
    for (i = 64; i < gic_irqs; i += 4)
	{
	   w32(GIC_DIST_BASE + GIC_DIST_PRI + i , 0x80808080);//Nonsecure中断分配80
	   //mprintf("interrupt priority register+%d is 0x%x\n", i,r32(GIC_DIST_BASE + GIC_DIST_PRI + i ));
	} 
	/*for (i = 0; i < gic_irqs; i += 16)
	{
	   w32(GIC_DIST_BASE + GIC_DIST_PRI + i , 0x30201000);
	   w32(GIC_DIST_BASE + GIC_DIST_PRI + i+4 , 0x70605040);
	   w32(GIC_DIST_BASE + GIC_DIST_PRI + i+8 , 0xb0a09080);
	   w32(GIC_DIST_BASE + GIC_DIST_PRI + i+12, 0xf0e0d0c0);
	}  */
	//mprintf( "Value of ICDIPR28 is 0x%x \n", r32(GIC_DIST_BASE + GIC_DIST_PRI + 28)); 
    //mprintf( "Value of ICDIPR32 is 0x%x \n", r32(GIC_DIST_BASE + GIC_DIST_PRI + 32));
  //  mprintf( "Value of ICDIPR36 is 0x%x \n", r32(GIC_DIST_BASE + GIC_DIST_PRI + 36));
	//mprintf( "Value of ICDIPR40 is 0x%x \n", r32(GIC_DIST_BASE + GIC_DIST_PRI + 40));
   // mprintf( "Value of ICDIPR64 is 0x%x \n", r32(GIC_DIST_BASE + GIC_DIST_PRI + 64));

	//mprintf( "Value of ICDICTR is 0x%x \n", r32(GIC_DIST_BASE + GIC_DIST_CTL));
	//mprintf( "Value of ICCIIDR is 0x%x \n", r32(GIC_CPU_BASE + GIC_CPU_IDENT));
	
	/*
	 * Set all interrupts sent to cpu0。在单核处理器中，该寄存器是RAZ/WI
	 */
	// for (i = 32; i < gic_irqs; i += 4)
    // w32(GIC_DIST_BASE + GIC_DIST_TARGET + i, 0x01010101);
     


	//ä½¿èƒ½æ‰€æœ‰ä¸­æ–­set enable   
/*
	for (i = 0; i <= gic_irqs; i += 32)
	{ 
		w32(GIC_DIST_BASE + GIC_DIST_ENABLE_SET + i/8 , 0xffffffff);
        //mprintf("interrupt set enable register+%d is 0x%x\n", i/8,r32(GIC_DIST_BASE + GIC_DIST_ENABLE_SET + i/8));
	}
	
	w32(GIC_DIST_BASE + GIC_DIST_ENABLE_CLEAR + 4, 0x2);
	*/
}

void gic_cpu_config(void)
{
	unsigned int i;

	/*
 	 * Deal with the banked PPI and SGIs - disable all PPI interrupts, ensure all SGI interrupts are enabled.
	 */
	//w32(GIC_DIST_BASE + GIC_DIST_ENABLE_CLEAR, 0xffff0000);
	//使能SGI和PPI
	//w32(GIC_DIST_BASE + GIC_DIST_ENABLE_SET, 0xffffffff);

	//中断屏蔽寄存器设置为0xff，不屏蔽中断
	w32(GIC_CPU_BASE + GIC_CPU_PRIMASK, 0xff);

	//ICCICR设置为0xf，secure中断产生FIQ，全部使能中断;若为0x7则secure产生IRQ中断
//	w32(GIC_CPU_BASE + GIC_CPU_CTRL, 0xf);
	
	w32(GIC_CPU_BASE + GIC_CPU_CTRL, 0x7);	
}

void gic_configure_irq(unsigned int irq, unsigned int type)
{
	unsigned int enablemask = 1 << (irq % 32);
	unsigned int enableoff = (irq / 32) * 4;
	unsigned int confmask = 0x2 << ((irq % 16) * 2);
	unsigned int confoff = (irq / 16) * 4;
	unsigned int enabled = FALSE;
	unsigned int val;

	/*
 	 * Read the current configuration register, and insert the config
	 * for hwirq, depending on interrupt type.
	 */
	val = r32(GIC_DIST_BASE + GIC_DIST_CONFIG + confoff);
	if (type == IRQ_TYPE_LEVEL_HIGH)
		val &= ~confmask;
	if (IRQ_TYPE_EDGE_RISING)
		val |= confmask;

	/*
 	 * As recommended by the spec, disable the interrupt before changing
	 * the configuration.
	 */
	if (r32(GIC_DIST_BASE + GIC_DIST_ENABLE_SET + enableoff) & enablemask){
		w32(GIC_DIST_BASE + GIC_DIST_ENABLE_CLEAR + enableoff, enablemask);
		enabled = TRUE;
	}

	/*
 	 * Write back the new configuration, and possibly re-enable
	 * the interrupt.
	 */
	w32(GIC_DIST_BASE + GIC_DIST_CONFIG + confoff, val);
	if (enabled)
		w32(GIC_DIST_BASE + GIC_DIST_ENABLE_SET + enableoff, enablemask);
}

///////////////////////////////// 
void gic_handle_irq_init(void)
{
	/*int i;
 mprintf("gic handle init\n");
	for(i = 0; i < MAX_IRQS; i++)
		irq_handle_vec[i] = NULL;*/
	mprintf("gic handle init done\n");
}

void gic_register_irq_entry(unsigned int irq, int_handle_func_ptr int_handle)
{
	if(irq >= 128)
		return;

	//mprintf("gic: register irq %d = %08x\n", irq, (u32)int_handle);
	irq_handle_vec[irq] = int_handle;
}

void gic_remove_irq_entry(unsigned int irq)
{
	if(irq >= 128)
		return;

	irq_handle_vec[irq] = NULL;
}

//中断处理函数

void gic_handle_irq(void)
{
	unsigned int irq;

//	mprintf("handle irq\n");

	do {
		irq = gic_get_irq_number();
		if(irq < MAX_IRQS) {
		//	mprintf("\r\ngic: handle irq %d\n", irq);
			if(NULL != irq_handle_vec[irq]) {
				gic_eoi_irq(irq);
				irq_handle_vec[irq](irq);
			}
			continue;
		}
		break;
	} while (1);
}


void gic_dist_init(void)
{
	//mprintf("gic dist init\n");
	gic_global_disable();//有些位在配置前必须先禁止中断
	gic_dist_config(MAX_IRQS);//
	gic_global_enable();
	//mprintf("gic dist init done\n");
}

void gic_cpu_init(void)
{
	//mprintf("gic cpu init\n");
	gic_cpu_config();
	
	//mprintf("gic cpu init done\n");
}

void disable_irq(void)
{

	unsigned long old,temp;
	__asm__ __volatile__("mrs %0, cpsr\n"
			     "orr %1, %0, #0xc0\n"
			     "msr cpsr_c, %1"
			     : "=r" (old), "=r" (temp)
			     :
			     : "memory");
	//return (old & 0x80) == 0;
}
//使能irq和fiq!!!
void enable_irq(void)
{
	unsigned long temp;
	//mprintf("enable interrupts\n");
	__asm__ __volatile__("mrs %0, cpsr\n"
			     "bic %0, %0, #0xc0\n"
			     "msr cpsr_c, %0"
			     : "=r" (temp)
			     :
			     : "memory");
	//mprintf("enable interrupts done\n");
}

void gic_init(void)
{
	//mprintf("gic init\n");
	gic_dist_init();
	gic_cpu_init();
	//gic_handle_irq_init();
	enable_irq();
	mprintf("gic init done\n");
	
}


void SWI_Enable(void)
{
	__asm__ __volatile__("svc 0x123\n"
					     :
					     :
					     :);

	
}  

  
