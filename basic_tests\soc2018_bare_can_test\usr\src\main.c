
#include <stdlib.h>
#include <stdint.h>
#include <float.h>
#include "types.h"
#include "gic.h"
#include "gpio.h" 
#include "bsp_print.h"
#include "can_sja1000.h"
#include "DRV_CAN.h"
//0x50000000 + DRV_CAN1_BASEADDRB
//DRV_PeliCAN_ACC_CODE0  DRV_PeliCAN_ACC_CODE0
void single_filter_extended_test(){//单过滤+拓展帧测试

    //在QEMU中reset阶段已经把clock设置为PeliCAN模式，所以不需要再设置
    uint8_t code_mask[]={//8个字节,地址为复位模式下的16~23,也即十六进制的0x10~0x17
        //4个ACR，在单过滤拓展帧下代表ID28~ID0+RTR(最后三位不用)【看手册的图】
        0xff,0xf8,0x07,0x80,
        //4个AMR（注意最后一个有RTR）,全部有效，1是'don't care'
        0x00,0x00,0x00,
        0x04 //注意屏蔽RTR位，否则会被过滤掉
    };

    uint8_t buffer_extended_right[]={//8个,地址为操作模式下的16~23
        //16.帧信息
        //1(拓展) RTR X X DLC3 DLC2 DLC1 DLC0 (X一般写0):1000 0011(DLC=3)
        0x83,
        //17.ID28~ID21; 18.ID20~ID13; 19.ID12~ID5; 20.ID4~ID0 000
         0xff,0xf8,0x07,0x80,  //1 111`1 111`1 111`1 000`0 000`0 111`1 000`0 000
        //21.22.23数据
        'A','B','C'//数据
    };


    //在复位模式下将code_mask写入接收端CAN1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE0,code_mask[0]);//ACR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE1,code_mask[1]);//ACR1   
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE2,code_mask[2]);//ACR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE3,code_mask[3]);//ACR3
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK0,code_mask[4]);//AMR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK1,code_mask[5]);//AMR1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK2,code_mask[6]);//AMR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK3,code_mask[7]);//AMR3

    //手动复位：接收端CAN1设置单过滤器模式+复位模式写0(尽管其初始化后续源码没有调用这个过滤器)
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_MODE,0x08);//0000 1000

    //发送端转入操作模式，接收端不用管
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_MODE,0x00);//0000 1000,操作模式+单过滤器（发送端没用到）

    //将正确ID的数据直接写入CAN0缓冲区(分别写入地址17~23,也即0x10,0x11,0x12,0x13,0x14,0x15,0x16),尽管地址和上面一样，但模式不同寄存器不同
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE0,buffer_extended_right[0]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE1,buffer_extended_right[1]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE2,buffer_extended_right[2]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE3,buffer_extended_right[3]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK0,buffer_extended_right[4]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK1,buffer_extended_right[5]);//A
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK2,buffer_extended_right[6]);//B
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK3,buffer_extended_right[7]);//C

    //触发CMR寄存器以发送数据：写第0位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据

    //将错误ID的数据数据直接写入CAN0缓冲区
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE0,buffer_extended_right[0]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE1,0xfe);//0xff才是正确的
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE2,buffer_extended_right[2]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE3,buffer_extended_right[3]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK0,buffer_extended_right[4]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK1,buffer_extended_right[5]);//A
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK2,buffer_extended_right[6]);//B
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK3,buffer_extended_right[7]);//C

    //触发CMR寄存器以清除接收缓冲区：写第2位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_COMMAND,0x04);//清除接收缓冲区

    //触发CMR寄存器以发送数据：写第0位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据

}


void single_filter_standard_test(){//单过滤+标准帧测试

    //在QEMU中reset阶段已经把clock设置为PeliCAN模式，所以不需要再设置
    uint8_t code_mask[]={//8个字节,地址为复位模式下的16~23,也即十六进制的0x10~0x17
        //4个ACR //ID=1111 1111 000(0x7F8) RTR=0 0000；data[1]=0x23 data[2]=0x24
        0xff,0x00,0x23,0x24,
        //4个AMR
        0x00,0x10,0x00,0x00 
    };

    uint8_t buffer_right[]={//6个,地址为操作模式下的16~21
        //16.帧信息0x10
        //0(标准) RTR X X DLC3 DLC2 DLC1 DLC0 (X一般写0):1000 0011(DLC=3)
        0x03,
        //17.18是ID，多余的填0
        0xff,0x00,
        //19.20.21数据
        0x23,0x24,'C'//数据
    };

    //在复位模式下将code_mask写入接收端CAN1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE0,code_mask[0]);//ACR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE1,code_mask[1]);//ACR1   
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE2,code_mask[2]);//ACR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE3,code_mask[3]);//ACR3
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK0,code_mask[4]);//AMR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK1,code_mask[5]);//AMR1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK2,code_mask[6]);//AMR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK3,code_mask[7]);//AMR3

    //手动复位：接收端CAN1设置单过滤器模式+复位模式写0(尽管其初始化后续源码没有调用这个过滤器)//不可能初始化
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_MODE,0x08);//0000 1000

    //发送端转入操作模式，接收端不用管
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_MODE,0x00);//0000 1000,操作模式+单过滤器（发送端没用到）

    //将正确ID的数据直接写入CAN0缓冲区(分别写入地址16~21,也即0x10,0x11,0x12,0x13,0x14,0x15,0x16),尽管地址和上面一样，但模式不同寄存器不同
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE0,buffer_right[0]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE1,buffer_right[1]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE2,buffer_right[2]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE3,buffer_right[3]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK0,buffer_right[4]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK1,buffer_right[5]);

    //触发CMR寄存器以发送数据：写第0位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据

    //将错误ID的数据直接写入CAN0缓冲区
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE0,buffer_right[0]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE1,0xfe);//0xff才是正确的
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE2,buffer_right[2]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE3,buffer_right[3]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK0,buffer_right[4]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK1,buffer_right[5]);

    //触发CMR寄存器以清除接收缓冲区：写第2位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_COMMAND,0x04);//清除接收缓冲区

    //触发CMR寄存器以发送数据：写第0位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据

    //将正确ID+第一字节错误数据直接写入CAN0缓冲区
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE0,buffer_right[0]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE1,buffer_right[1]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE2,buffer_right[2]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE3,0x25);//0x23才可以通过
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK0,buffer_right[4]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK1,buffer_right[5]);

    //触发CMR寄存器以发送数据：写第0位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据
}

void dual_filter_extended_test(){//双过滤+拓展帧测试
    
    //在QEMU中reset阶段已经把clock设置为PeliCAN模式，所以不需要再设置
    uint8_t code_mask[]={//8个字节,地址为复位模式下的16~23,也即十六进制的0x10~0x17
        //4个ACR 
        0xff,0x00,//第一组ID
        0x00,0xff,//第二组ID
        //4个AMR
        0x00,0x00,//第一组mask
        0x00,0x00 //第二组mask
    };

    uint8_t buffer_extended_right[]={//8个,地址为操作模式下的16~23 //和第一组ID匹配
        //16.帧信息
        //1(拓展) RTR X X DLC3 DLC2 DLC1 DLC0 (X一般写0):1000 0011(DLC=3)
        0x83,
        //17.ID28~ID21; 18.ID20~ID13; 【19.ID12~ID5; 20.ID4~ID0 000无效】
         0xff,0x00,//有效ID
         0x07,0x80,//无效ID
        //21.22.23数据
        'A','B','C'//数据
    };

    //在复位模式下将code_mask写入接收端CAN1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE0,code_mask[0]);//ACR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE1,code_mask[1]);//ACR1   
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE2,code_mask[2]);//ACR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE3,code_mask[3]);//ACR3
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK0,code_mask[4]);//AMR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK1,code_mask[5]);//AMR1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK2,code_mask[6]);//AMR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK3,code_mask[7]);//AMR3

    //手动复位：接收端CAN1设置双过滤器模式+复位模式写0(尽管其初始化后续源码没有调用这个过滤器)//不可能初始化
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_MODE,0x00);//0000 0000

    //发送端转入操作模式，接收端不用管
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_MODE,0x00);//0000 0000,操作模式+双过滤器（发送端没用到）

    //1.仅和ID1匹配
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE0,buffer_extended_right[0]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE1,buffer_extended_right[1]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE2,buffer_extended_right[2]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE3,buffer_extended_right[3]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK0,buffer_extended_right[4]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK1,buffer_extended_right[5]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK2,buffer_extended_right[6]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK3,buffer_extended_right[7]);

    //触发CMR寄存器以发送数据：写第0位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据

    //2.仅和ID2匹配
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE0,buffer_extended_right[0]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE1,0x00);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE2,0xff);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE3,buffer_extended_right[3]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK0,buffer_extended_right[4]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK1,'D');
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK2,'E');
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK3,'F');

    //触发CMR寄存器以清除接收缓冲区：写第2位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_COMMAND,0x04);//清除接收缓冲区

    //触发CMR寄存器以发送数据：写第0位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据

    //3.都不匹配
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE0,0x0f);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE1,0xf0);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE2,buffer_extended_right[2]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE3,buffer_extended_right[3]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK0,buffer_extended_right[4]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK1,'G');
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK2,'H');
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK3,'I');

    //触发CMR寄存器以发送数据：写第0位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据

}

void dual_filter_standard_test(){//双过滤+标准帧测试
        //在QEMU中reset阶段已经把clock设置为PeliCAN模式，所以不需要再设置
    uint8_t code_mask[]={//8个字节,地址为复位模式下的16~23,也即十六进制的0x10~0x17
        //4个ACR 
        0xff,0x0f,//第一组ID+半个字节：ID：1111 1111 000 RTR=0;DATA7~4：1111
        0x00,0xe0,//第二组ID+半个字节：ID：0000 0000 111 RTR=0;DATA3~0：0000
        //4个AMR
        0x00,0x10,//第一组mask+RTR屏蔽
        0x00,0x10 //第二组mask+RTR屏蔽
    };

    uint8_t buffer_right[]={//8个,地址为操作模式下的16~21//和第一组ID匹配
        //16.帧信息0x10
        //0(标准) RTR X X DLC3 DLC2 DLC1 DLC0 (X一般写0):1000 0011(DLC=3)
        0x03,
        //17.18是ID，多余的填0
        0xff,0x00,
        //19.20.21数据
        0xf0,//前1个字节匹配
        'B','C'//数据
    };

    //在复位模式下将code_mask写入接收端CAN1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE0,code_mask[0]);//ACR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE1,code_mask[1]);//ACR1   
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE2,code_mask[2]);//ACR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE3,code_mask[3]);//ACR3
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK0,code_mask[4]);//AMR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK1,code_mask[5]);//AMR1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK2,code_mask[6]);//AMR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK3,code_mask[7]);//AMR3

    //手动复位：接收端CAN1设置双过滤器模式+复位模式写0(尽管其初始化后续源码没有调用这个过滤器)
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_MODE,0x00);//0000 0000

    //发送端转入操作模式，接收端不用管
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_MODE,0x00);//0000 0000,操作模式+双过滤器（发送端没用到）

    //1.仅和ID1+前1字节匹配
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE0,buffer_right[0]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE1,buffer_right[1]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE2,buffer_right[2]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE3,buffer_right[3]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK0,buffer_right[4]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK1,buffer_right[5]);

    //触发CMR寄存器以发送数据：写第0位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据

    //2.仅和ID1+前1字节不匹配
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE0,buffer_right[0]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE1,buffer_right[1]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE2,buffer_right[2]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE3,0x03);//0xf0是正确的
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK0,buffer_right[4]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK1,buffer_right[5]);

    //触发CMR寄存器以清除接收缓冲区：写第2位为1
    //WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_COMMAND,0x04);//清除接收缓冲区

    //触发CMR寄存器以发送数据：写第0位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据

    //3.仅和ID2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE0,buffer_right[0]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE1,0x00);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE2,0xe0);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE3,0x00);//数据都不匹配，无影响
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK0,buffer_right[4]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK1,buffer_right[5]);

    //触发CMR寄存器以发送数据：写第0位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据
}


void sleep_mode_test(){//睡眠模式测试
    //在QEMU中reset阶段已经把clock设置为PeliCAN模式，所以不需要再设置
    uint8_t code_mask[]={//8个字节,地址为复位模式下的16~23,也即十六进制的0x10~0x17
        //4个ACR，在单过滤拓展帧下代表ID28~ID0+RTR(最后三位不用)【看手册的图】
        0xff,0xf8,0x07,0x80,
        //4个AMR（注意最后一个有RTR）,全部有效，1是'don't care'
        0x00,0x00,0x00,
        0x04 //注意屏蔽RTR位，否则会被过滤掉
    };

    uint8_t buffer_extended_right[]={//8个,地址为操作模式下的16~23
        //16.帧信息
        //1(拓展) RTR X X DLC3 DLC2 DLC1 DLC0 (X一般写0):1000 0011(DLC=3)
        0x83,
        //17.ID28~ID21; 18.ID20~ID13; 19.ID12~ID5; 20.ID4~ID0 000
         0xff,0xf8,0x07,0x80,  //1 111`1 111`1 111`1 000`0 000`0 111`1 000`0 000
        //21.22.23数据
        'A','B','C'//数据
    };

    //在复位模式下将code_mask写入接收端CAN1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE0,code_mask[0]);//ACR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE1,code_mask[1]);//ACR1   
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE2,code_mask[2]);//ACR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE3,code_mask[3]);//ACR3
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK0,code_mask[4]);//AMR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK1,code_mask[5]);//AMR1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK2,code_mask[6]);//AMR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK3,code_mask[7]);//AMR3

    //接收端CAN1设置单过滤器模式+操作模式+睡眠模式(尽管其初始化后续源码没有调用这个过滤器)
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_MODE,0x18);//0001 1000

    //发送端CAN0转入操作模式，接收端不用管
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_MODE,0x08);//0000 1000,操作模式+单过滤器+唤醒模式（发送端没用到）

    //将正确ID的数据直接写入CAN0缓冲区(分别写入地址17~23,也即0x10,0x11,0x12,0x13,0x14,0x15,0x16),尽管地址和上面一样，但模式不同寄存器不同
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE0,buffer_extended_right[0]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE1,buffer_extended_right[1]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE2,buffer_extended_right[2]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE3,buffer_extended_right[3]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK0,buffer_extended_right[4]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK1,buffer_extended_right[5]);//A
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK2,buffer_extended_right[6]);//B
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK3,buffer_extended_right[7]);//C

    //触发CMR寄存器以发送数据：写第0位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据

    //将错误ID的数据数据直接写入CAN0缓冲区
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE0,buffer_extended_right[0]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE1,0xfe);//0xff才是正确的
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE2,buffer_extended_right[2]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE3,buffer_extended_right[3]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK0,buffer_extended_right[4]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK1,buffer_extended_right[5]);//A
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK2,buffer_extended_right[6]);//B
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK3,buffer_extended_right[7]);//C

    //触发CMR寄存器以清除接收缓冲区：写第2位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_COMMAND,0x04);//清除接收缓冲区

    //触发CMR寄存器以发送数据：写第0位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据
}


void selftest_mode_test(){//睡眠模式测试
    //在QEMU中reset阶段已经把clock设置为PeliCAN模式，所以不需要再设置
    uint8_t code_mask[]={//8个字节,地址为复位模式下的16~23,也即十六进制的0x10~0x17
        //4个ACR，在单过滤拓展帧下代表ID28~ID0+RTR(最后三位不用)【看手册的图】
        0xff,0xf8,0x07,0x80,
        //4个AMR（注意最后一个有RTR）,全部有效，1是'don't care'
        0x00,0x00,0x00,
        0x04 //注意屏蔽RTR位，否则会被过滤掉
    };

    uint8_t buffer_extended_right[]={//8个,地址为操作模式下的16~23
        //16.帧信息
        //1(拓展) RTR X X DLC3 DLC2 DLC1 DLC0 (X一般写0):1000 0011(DLC=3)
        0x83,
        //17.ID28~ID21; 18.ID20~ID13; 19.ID12~ID5; 20.ID4~ID0 000
         0xff,0xf8,0x07,0x80,  //1 111`1 111`1 111`1 000`0 000`0 111`1 000`0 000
        //21.22.23数据
        'A','B','C'//数据
    };

    //在复位模式下将code_mask写入接收端CAN1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE0,code_mask[0]);//ACR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE1,code_mask[1]);//ACR1   
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE2,code_mask[2]);//ACR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE3,code_mask[3]);//ACR3
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK0,code_mask[4]);//AMR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK1,code_mask[5]);//AMR1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK2,code_mask[6]);//AMR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK3,code_mask[7]);//AMR3

    //发送端CAN1设置单过滤器模式+操作模式+自测模式(尽管其初始化后续源码没有调用这个过滤器)
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_MODE,0x0C);//0000 1100

    //将正确ID的数据直接写入CAN1缓冲区(分别写入地址17~23,也即0x10,0x11,0x12,0x13,0x14,0x15,0x16),尽管地址和上面一样，但模式不同寄存器不同
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE0,buffer_extended_right[0]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE1,buffer_extended_right[1]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE2,buffer_extended_right[2]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE3,buffer_extended_right[3]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK0,buffer_extended_right[4]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK1,buffer_extended_right[5]);//A
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK2,buffer_extended_right[6]);//B
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK3,buffer_extended_right[7]);//C

    //触发CMR寄存器以发送自测数据：写4号位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_COMMAND,0x10);//发送自测数据

    
    READ_8(0x50000000 + DRV_CAN1_BASEADDRB+0x10);
    READ_8(0x50000000 + DRV_CAN1_BASEADDRB+0x11);
    READ_8(0x50000000 + DRV_CAN1_BASEADDRB+0x12);
    READ_8(0x50000000 + DRV_CAN1_BASEADDRB+0x13);
    READ_8(0x50000000 + DRV_CAN1_BASEADDRB+0x14);
    READ_8(0x50000000 + DRV_CAN1_BASEADDRB+0x15);
    READ_8(0x50000000 + DRV_CAN1_BASEADDRB+0x16);//0x16=0001 0110,2+4+16=22
    READ_8(0x50000000 + DRV_CAN1_BASEADDRB+0x17);
}


void ListenOnly_mode_test(){//只听模式测试
    //在QEMU中reset阶段已经把clock设置为PeliCAN模式，所以不需要再设置
    uint8_t code_mask[]={//8个字节,地址为复位模式下的16~23,也即十六进制的0x10~0x17
        //4个ACR，在单过滤拓展帧下代表ID28~ID0+RTR(最后三位不用)【看手册的图】
        0xff,0xf8,0x07,0x80,
        //4个AMR（注意最后一个有RTR）,全部有效，1是'don't care'
        0x00,0x00,0x00,
        0x04 //注意屏蔽RTR位，否则会被过滤掉
    };

    uint8_t buffer_extended_right[]={//8个,地址为操作模式下的16~23
        //16.帧信息
        //1(拓展) RTR X X DLC3 DLC2 DLC1 DLC0 (X一般写0):1000 0011(DLC=3)
        0x83,
        //17.ID28~ID21; 18.ID20~ID13; 19.ID12~ID5; 20.ID4~ID0 000
         0xff,0xf8,0x07,0x80,  //1 111`1 111`1 111`1 000`0 000`0 111`1 000`0 000
        //21.22.23数据
        'A','B','C'//数据
    };

    //在复位模式下将code_mask写入接收端CAN1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE0,code_mask[0]);//ACR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE1,code_mask[1]);//ACR1   
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE2,code_mask[2]);//ACR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE3,code_mask[3]);//ACR3
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK0,code_mask[4]);//AMR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK1,code_mask[5]);//AMR1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK2,code_mask[6]);//AMR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK3,code_mask[7]);//AMR3

    //接收端CAN1设置单过滤器模式+操作模式+只听模式(尽管其初始化后续源码没有调用这个过滤器)
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_MODE,0x0A);//0001 1010

    //发送端CAN0转入操作模式，接收端不用管
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_MODE,0x08);//0000 1000,操作模式+单过滤器+唤醒模式（发送端没用到）

    //将正确ID的数据直接写入CAN0缓冲区(分别写入地址17~23,也即0x10,0x11,0x12,0x13,0x14,0x15,0x16),尽管地址和上面一样，但模式不同寄存器不同
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE0,buffer_extended_right[0]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE1,buffer_extended_right[1]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE2,buffer_extended_right[2]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE3,buffer_extended_right[3]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK0,buffer_extended_right[4]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK1,buffer_extended_right[5]);//A
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK2,buffer_extended_right[6]);//B
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK3,buffer_extended_right[7]);//C

    //CAN0触发CMR寄存器以发送数据：写第0位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据

    //CAN1触发CMR寄存器以发送数据：写第0位为1（只听模式下发送失败）
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_COMMAND,0x01);//发送数据
}


void RI_test(){//接收中断测试
    //在QEMU中reset阶段已经把clock设置为PeliCAN模式，所以不需要再设置
    uint8_t code_mask[]={//8个字节,地址为复位模式下的16~23,也即十六进制的0x10~0x17
        //4个ACR，在单过滤拓展帧下代表ID28~ID0+RTR(最后三位不用)【看手册的图】
        0xff,0xf8,0x07,0x80,
        //4个AMR（注意最后一个有RTR）,全部有效，1是'don't care'
        0x00,0x00,0x00,
        0x04 //注意屏蔽RTR位，否则会被过滤掉
    };

    uint8_t buffer_extended_right[]={//8个,地址为操作模式下的16~23
        //16.帧信息
        //1(拓展) RTR X X DLC3 DLC2 DLC1 DLC0 (X一般写0):1000 0011(DLC=3)
        0x83,
        //17.ID28~ID21; 18.ID20~ID13; 19.ID12~ID5; 20.ID4~ID0 000
         0xff,0xf8,0x07,0x80,  //1 111`1 111`1 111`1 000`0 000`0 111`1 000`0 000
        //21.22.23数据
        'A','B','C'//数据
    };

    //在复位模式下将code_mask写入接收端CAN1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE0,code_mask[0]);//ACR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE1,code_mask[1]);//ACR1   
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE2,code_mask[2]);//ACR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE3,code_mask[3]);//ACR3
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK0,code_mask[4]);//AMR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK1,code_mask[5]);//AMR1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK2,code_mask[6]);//AMR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK3,code_mask[7]);//AMR3

    //接收端CAN1设置单过滤器模式+操作模式+只听模式(尽管其初始化后续源码没有调用这个过滤器)
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_MODE,0x0A);//0001 1010
    //使能CAN1的接收中断，此时在接收端自动设置接收中断标志位，在更新中断函数中同时判断使能和标志位;当rxmsg_cnt == 0 后会自动清除并拉低GIC
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_INTR_ENABLE,0x01);//0000 0001,【使能】接收端的接收中断

    //发送端CAN0转入操作模式，接收端不用管
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_MODE,0x08);//0000 1000,操作模式+单过滤器+唤醒模式（发送端没用到）

    //将正确ID的数据直接写入CAN0缓冲区(分别写入地址17~23,也即0x10,0x11,0x12,0x13,0x14,0x15,0x16),尽管地址和上面一样，但模式不同寄存器不同
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE0,buffer_extended_right[0]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE1,buffer_extended_right[1]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE2,buffer_extended_right[2]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE3,buffer_extended_right[3]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK0,buffer_extended_right[4]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK1,buffer_extended_right[5]);//A
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK2,buffer_extended_right[6]);//B
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK3,buffer_extended_right[7]);//C

    //CAN0触发CMR寄存器以发送数据：写第0位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据
}


void TI_test(){//发送中断测试
    //在QEMU中reset阶段已经把clock设置为PeliCAN模式，所以不需要再设置
    uint8_t code_mask[]={//8个字节,地址为复位模式下的16~23,也即十六进制的0x10~0x17
        //4个ACR，在单过滤拓展帧下代表ID28~ID0+RTR(最后三位不用)【看手册的图】
        0xff,0xf8,0x07,0x80,
        //4个AMR（注意最后一个有RTR）,全部有效，1是'don't care'
        0x00,0x00,0x00,
        0x04 //注意屏蔽RTR位，否则会被过滤掉
    };

    uint8_t buffer_extended_right[]={//8个,地址为操作模式下的16~23
        //16.帧信息
        //1(拓展) RTR X X DLC3 DLC2 DLC1 DLC0 (X一般写0):1000 0011(DLC=3)
        0x83,
        //17.ID28~ID21; 18.ID20~ID13; 19.ID12~ID5; 20.ID4~ID0 000
         0xff,0xf8,0x07,0x80,  //1 111`1 111`1 111`1 000`0 000`0 111`1 000`0 000
        //21.22.23数据
        'A','B','C'//数据
    };

    //在复位模式下将code_mask写入接收端CAN1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE0,code_mask[0]);//ACR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE1,code_mask[1]);//ACR1   
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE2,code_mask[2]);//ACR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE3,code_mask[3]);//ACR3
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK0,code_mask[4]);//AMR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK1,code_mask[5]);//AMR1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK2,code_mask[6]);//AMR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK3,code_mask[7]);//AMR3

    //接收端CAN1设置单过滤器模式+操作模式+只听模式(尽管其初始化后续源码没有调用这个过滤器)
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_MODE,0x0A);//0000 1010
    //使能CAN1的接收中断，此时在接收端自动设置接收中断标志位，在更新中断函数中同时判断使能和标志位;当rxmsg_cnt == 0 后会自动清除并拉低GIC
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_INTR_ENABLE,0x02);//0000 0010,【使能】发送端的发送中断

    //发送端CAN0转入操作模式，接收端不用管
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_MODE,0x08);//0000 1000,操作模式+单过滤器+唤醒模式（发送端没用到）

    //将正确ID的数据直接写入CAN0缓冲区(分别写入地址17~23,也即0x10,0x11,0x12,0x13,0x14,0x15,0x16),尽管地址和上面一样，但模式不同寄存器不同
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE0,buffer_extended_right[0]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE1,buffer_extended_right[1]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE2,buffer_extended_right[2]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE3,buffer_extended_right[3]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK0,buffer_extended_right[4]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK1,buffer_extended_right[5]);//A
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK2,buffer_extended_right[6]);//B
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK3,buffer_extended_right[7]);//C

    //CAN0触发CMR寄存器以发送数据：写第0位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据
}

void DOI_test(){//数据溢出中断测试
    //在QEMU中reset阶段已经把clock设置为PeliCAN模式，所以不需要再设置
    uint8_t code_mask[]={//8个字节,地址为复位模式下的16~23,也即十六进制的0x10~0x17
        //4个ACR，在单过滤拓展帧下代表ID28~ID0+RTR(最后三位不用)【看手册的图】
        0xff,0xf8,0x07,0x80,
        //4个AMR（注意最后一个有RTR）,全部有效，1是'don't care'
        0x00,0x00,0x00,
        0x04 //注意屏蔽RTR位，否则会被过滤掉
    };

    uint8_t buffer_extended_right[]={//8个,地址为操作模式下的16~23
        //16.帧信息
        //1(拓展) RTR X X DLC3 DLC2 DLC1 DLC0 (X一般写0):1000 0011(DLC=3)
        0x83,
        //17.ID28~ID21; 18.ID20~ID13; 19.ID12~ID5; 20.ID4~ID0 000
         0xff,0xf8,0x07,0x80,  //1 111`1 111`1 111`1 000`0 000`0 111`1 000`0 000
        //21.22.23数据
        'A','B','C'//数据
        //24 25 26 27 28 29 (30)
    };

    //在复位模式下将code_mask写入接收端CAN1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE0,code_mask[0]);//ACR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE1,code_mask[1]);//ACR1   
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE2,code_mask[2]);//ACR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE3,code_mask[3]);//ACR3
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK0,code_mask[4]);//AMR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK1,code_mask[5]);//AMR1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK2,code_mask[6]);//AMR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK3,code_mask[7]);//AMR3

    //接收端CAN1设置单过滤器模式+操作模式+只听模式(尽管其初始化后续源码没有调用这个过滤器)
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_MODE,0x0A);//0001 1010
    //使能CAN1的接收中断，此时在接收端自动设置接收中断标志位，在更新中断函数中同时判断使能和标志位;当rxmsg_cnt == 0 后会自动清除并拉低GIC
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_INTR_ENABLE,0x08);//0000 1000,【使能】接收端的数据溢出中断

    //发送端CAN0转入操作模式，接收端不用管
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_MODE,0x08);//0000 1000,操作模式+单过滤器+唤醒模式（发送端没用到）

    //将正确ID的数据直接写入CAN0缓冲区(分别写入地址17~23,也即0x10,0x11,0x12,0x13,0x14,0x15,0x16),尽管地址和上面一样，但模式不同寄存器不同
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE0,buffer_extended_right[0]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE1,buffer_extended_right[1]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE2,buffer_extended_right[2]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE3,buffer_extended_right[3]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK0,buffer_extended_right[4]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK1,buffer_extended_right[5]);//A
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK2,buffer_extended_right[6]);//B
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK3,buffer_extended_right[7]);//C

    //CAN0触发CMR寄存器以发送数据：写第0位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据(溢出)
}

void WUI_test(){//唤醒中断测试
    //在QEMU中reset阶段已经把clock设置为PeliCAN模式，所以不需要再设置
    uint8_t code_mask[]={//8个字节,地址为复位模式下的16~23,也即十六进制的0x10~0x17
        //4个ACR，在单过滤拓展帧下代表ID28~ID0+RTR(最后三位不用)【看手册的图】
        0xff,0xf8,0x07,0x80,
        //4个AMR（注意最后一个有RTR）,全部有效，1是'don't care'
        0x00,0x00,0x00,
        0x04 //注意屏蔽RTR位，否则会被过滤掉
    };

    uint8_t buffer_extended_right[]={//8个,地址为操作模式下的16~23
        //16.帧信息
        //1(拓展) RTR X X DLC3 DLC2 DLC1 DLC0 (X一般写0):1000 0011(DLC=3)
        0x83,
        //17.ID28~ID21; 18.ID20~ID13; 19.ID12~ID5; 20.ID4~ID0 000
         0xff,0xf8,0x07,0x80,  //1 111`1 111`1 111`1 000`0 000`0 111`1 000`0 000
        //21.22.23数据
        'A','B','C'//数据
        //24 25 26 27 28 29 (30)
    };

    //在复位模式下将code_mask写入接收端CAN1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE0,code_mask[0]);//ACR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE1,code_mask[1]);//ACR1   
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE2,code_mask[2]);//ACR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE3,code_mask[3]);//ACR3
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK0,code_mask[4]);//AMR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK1,code_mask[5]);//AMR1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK2,code_mask[6]);//AMR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK3,code_mask[7]);//AMR3

    //接收端CAN1设置单过滤器模式+操作模式+睡眠模式(尽管其初始化后续源码没有调用这个过滤器)
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_MODE,0x18);//0001 1000
    //使能CAN1的接收中断，此时在接收端自动设置接收中断标志位，在更新中断函数中同时判断使能和标志位;当rxmsg_cnt == 0 后会自动清除并拉低GIC
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_INTR_ENABLE,0x10);//0001 0000,【使能】接收端的唤醒中断

    //发送端CAN0转入操作模式，接收端不用管
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_MODE,0x08);//0000 1000,操作模式+单过滤器+唤醒模式（发送端没用到）

    //将正确ID的数据直接写入CAN0缓冲区(分别写入地址17~23,也即0x10,0x11,0x12,0x13,0x14,0x15,0x16),尽管地址和上面一样，但模式不同寄存器不同
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE0,buffer_extended_right[0]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE1,buffer_extended_right[1]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE2,buffer_extended_right[2]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE3,buffer_extended_right[3]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK0,buffer_extended_right[4]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK1,buffer_extended_right[5]);//A
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK2,buffer_extended_right[6]);//B
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK3,buffer_extended_right[7]);//C

    //CAN0触发CMR寄存器以发送数据：写第0位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据
}

void inject96_test(){//错误注入96测试
    //在QEMU中reset阶段已经把clock设置为PeliCAN模式，所以不需要再设置
    uint8_t code_mask[]={//8个字节,地址为复位模式下的16~23,也即十六进制的0x10~0x17
        //4个ACR，在单过滤拓展帧下代表ID28~ID0+RTR(最后三位不用)【看手册的图】
        0xff,0xf8,0x07,0x80,
        //4个AMR（注意最后一个有RTR）,全部有效，1是'don't care'
        0x00,0x00,0x00,
        0x04 //注意屏蔽RTR位，否则会被过滤掉
    };

    uint8_t buffer_extended_right[]={//8个,地址为操作模式下的16~23
        //16.帧信息
        //1(拓展) RTR X X DLC3 DLC2 DLC1 DLC0 (X一般写0):1000 0011(DLC=3)
        0x83,
        //17.ID28~ID21; 18.ID20~ID13; 19.ID12~ID5; 20.ID4~ID0 000
         0xff,0xf8,0x07,0x80,  //1 111`1 111`1 111`1 000`0 000`0 111`1 000`0 000
        //21.22.23数据
        'A','B','C'//数据
        //24 25 26 27 28 29 (30)
    };

    //在复位模式下将code_mask写入接收端CAN1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE0,code_mask[0]);//ACR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE1,code_mask[1]);//ACR1   
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE2,code_mask[2]);//ACR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE3,code_mask[3]);//ACR3
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK0,code_mask[4]);//AMR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK1,code_mask[5]);//AMR1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK2,code_mask[6]);//AMR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK3,code_mask[7]);//AMR3

    //接收端CAN1设置单过滤器模式+操作模式+唤醒模式(尽管其初始化后续源码没有调用这个过滤器)
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_MODE,0x08);//0000 1000
    //使能CAN1的接收中断，此时在接收端自动设置接收中断标志位，在更新中断函数中同时判断使能和标志位;当rxmsg_cnt == 0 后会自动清除并拉低GIC
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_INTR_ENABLE,0x04);//0000 0100,【使能】接收端的错误警告中断

    //发送端CAN0转入操作模式，接收端不用管
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_MODE,0x08);//0000 1000,操作模式+单过滤器+唤醒模式（发送端没用到）

    //将正确ID的数据直接写入CAN0缓冲区(分别写入地址17~23,也即0x10,0x11,0x12,0x13,0x14,0x15,0x16),尽管地址和上面一样，但模式不同寄存器不同
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE0,buffer_extended_right[0]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE1,buffer_extended_right[1]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE2,buffer_extended_right[2]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE3,buffer_extended_right[3]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK0,buffer_extended_right[4]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK1,buffer_extended_right[5]);//A
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK2,buffer_extended_right[6]);//B
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK3,buffer_extended_right[7]);//C

    //CAN0触发CMR寄存器以发送数据：写第0位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据
}

void inject127_test(){//错误注入127测试
    //在QEMU中reset阶段已经把clock设置为PeliCAN模式，所以不需要再设置
    uint8_t code_mask[]={//8个字节,地址为复位模式下的16~23,也即十六进制的0x10~0x17
        //4个ACR，在单过滤拓展帧下代表ID28~ID0+RTR(最后三位不用)【看手册的图】
        0xff,0xf8,0x07,0x80,
        //4个AMR（注意最后一个有RTR）,全部有效，1是'don't care'
        0x00,0x00,0x00,
        0x04 //注意屏蔽RTR位，否则会被过滤掉
    };

    uint8_t buffer_extended_right[]={//8个,地址为操作模式下的16~23
        //16.帧信息
        //1(拓展) RTR X X DLC3 DLC2 DLC1 DLC0 (X一般写0):1000 0011(DLC=3)
        0x83,
        //17.ID28~ID21; 18.ID20~ID13; 19.ID12~ID5; 20.ID4~ID0 000
         0xff,0xf8,0x07,0x80,  //1 111`1 111`1 111`1 000`0 000`0 111`1 000`0 000
        //21.22.23数据
        'A','B','C'//数据
        //24 25 26 27 28 29 (30)
    };

    //在复位模式下将code_mask写入接收端CAN1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE0,code_mask[0]);//ACR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE1,code_mask[1]);//ACR1   
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE2,code_mask[2]);//ACR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE3,code_mask[3]);//ACR3
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK0,code_mask[4]);//AMR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK1,code_mask[5]);//AMR1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK2,code_mask[6]);//AMR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK3,code_mask[7]);//AMR3

    //接收端CAN1设置单过滤器模式+操作模式+唤醒模式(尽管其初始化后续源码没有调用这个过滤器)
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_MODE,0x08);//0000 1000
    //使能CAN1的接收中断，此时在接收端自动设置接收中断标志位，在更新中断函数中同时判断使能和标志位;当rxmsg_cnt == 0 后会自动清除并拉低GIC
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_INTR_ENABLE,0x20);//0010 0000,【使能】接收端的错误被动中断

    //发送端CAN0转入操作模式，接收端不用管
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_MODE,0x08);//0000 1000,操作模式+单过滤器+唤醒模式（发送端没用到）

    //将正确ID的数据直接写入CAN0缓冲区(分别写入地址17~23,也即0x10,0x11,0x12,0x13,0x14,0x15,0x16),尽管地址和上面一样，但模式不同寄存器不同
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE0,buffer_extended_right[0]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE1,buffer_extended_right[1]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE2,buffer_extended_right[2]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE3,buffer_extended_right[3]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK0,buffer_extended_right[4]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK1,buffer_extended_right[5]);//A
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK2,buffer_extended_right[6]);//B
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK3,buffer_extended_right[7]);//C

    //CAN0触发CMR寄存器以发送数据：写第0位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据
}

void inject255_test(){//错误注入255测试
    //在QEMU中reset阶段已经把clock设置为PeliCAN模式，所以不需要再设置
    uint8_t code_mask[]={//8个字节,地址为复位模式下的16~23,也即十六进制的0x10~0x17
        //4个ACR，在单过滤拓展帧下代表ID28~ID0+RTR(最后三位不用)【看手册的图】
        0xff,0xf8,0x07,0x80,
        //4个AMR（注意最后一个有RTR）,全部有效，1是'don't care'
        0x00,0x00,0x00,
        0x04 //注意屏蔽RTR位，否则会被过滤掉
    };

    uint8_t buffer_extended_right[]={//8个,地址为操作模式下的16~23
        //16.帧信息
        //1(拓展) RTR X X DLC3 DLC2 DLC1 DLC0 (X一般写0):1000 0011(DLC=3)
        0x83,
        //17.ID28~ID21; 18.ID20~ID13; 19.ID12~ID5; 20.ID4~ID0 000
         0xff,0xf8,0x07,0x80,  //1 111`1 111`1 111`1 000`0 000`0 111`1 000`0 000
        //21.22.23数据
        'A','B','C'//数据
        //24 25 26 27 28 29 (30)
    };

    //在复位模式下将code_mask写入接收端CAN1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE0,code_mask[0]);//ACR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE1,code_mask[1]);//ACR1   
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE2,code_mask[2]);//ACR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_CODE3,code_mask[3]);//ACR3
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK0,code_mask[4]);//AMR0
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK1,code_mask[5]);//AMR1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK2,code_mask[6]);//AMR2
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_ACC_MASK3,code_mask[7]);//AMR3

    //接收端CAN1设置单过滤器模式+操作模式+唤醒模式(尽管其初始化后续源码没有调用这个过滤器)
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_MODE,0x08);//0000 1000
    //使能CAN1的接收中断，此时在接收端自动设置接收中断标志位，在更新中断函数中同时判断使能和标志位;当rxmsg_cnt == 0 后会自动清除并拉低GIC
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRB+DRV_PeliCAN_INTR_ENABLE,0x04);//0000 0100,【使能】接收端的错误警告中断

    //发送端CAN0转入操作模式，接收端不用管
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_MODE,0x08);//0000 1000,操作模式+单过滤器+唤醒模式（发送端没用到）

    //将正确ID的数据直接写入CAN0缓冲区(分别写入地址17~23,也即0x10,0x11,0x12,0x13,0x14,0x15,0x16),尽管地址和上面一样，但模式不同寄存器不同
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE0,buffer_extended_right[0]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE1,buffer_extended_right[1]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE2,buffer_extended_right[2]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_CODE3,buffer_extended_right[3]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK0,buffer_extended_right[4]);
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK1,buffer_extended_right[5]);//A
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK2,buffer_extended_right[6]);//B
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_ACC_MASK3,buffer_extended_right[7]);//C

    //CAN0触发CMR寄存器以发送数据：写第0位为1
    WRITE_8(0x50000000 + DRV_CAN1_BASEADDRA+DRV_PeliCAN_COMMAND,0x01);//发送数据
}

//为了方便，定义结构体表示CAN参数
typedef struct{
    U32 Can_no;//通道号
    U32 baudrate;//波特率
    U32 id;//识别码
    U32 mask;//掩码 
    U8 intr;//中断使能
}CAN_Parameter;

void DRV_CAN_test(){//测试使用驱动函数（单过滤器+标准帧）
    // uint8_t code_mask[]={//8个字节,地址为复位模式下的16~23,也即十六进制的0x10~0x17
    //     //4个ACR //ID=1111 1111 000(0x7F8) RTR=0 0000；data[1]=0x23 data[2]=0x24
    //     0xff,0x00,0x23,0x24,
    //     //4个AMR
    //     0x00,0x10,0x00,0x00 
    // };
    
    CAN_Parameter can0={//发送端CAN0
        //通道号
        .Can_no=0,
        //波特率
        .baudrate=500000,
        //识别码
        .id=0x242300ff,
        //掩码
        .mask= 0x00001000,
        //中断使能
       .intr=0x00,
    };

    CAN_Parameter can1={//接收端CAN1
        //通道号
        .Can_no=1,
        //波特率
        .baudrate=500000,
        //识别码
        .id=0x242300ff,
        //掩码
        .mask= 0x00001000,
        //中断使能
       .intr=0x00,
    };

        CAN_Parameter can2={//发送端CAN2
        //通道号
        .Can_no=2,
        //波特率
        .baudrate=500000,
        //识别码
        .id=0x242300ff,
        //掩码
        .mask= 0x00001000,
        //中断使能
       .intr=0x00,
    };

        CAN_Parameter can3={//发送端CAN3
        //通道号
        .Can_no=3,
        //波特率
        .baudrate=500000,
        //识别码
        .id=0x242300ff,
        //掩码
        .mask= 0x00001000,
        //中断使能
       .intr=0x00,
    };

    //初始化发送端CAN0
    Drv_Can_Init(can0.Can_no,can0.baudrate,can0.id,can0.mask,can0.intr);
    //初始化接收端CAN1
    Drv_Can_Init(can1.Can_no,can1.baudrate,can1.id,can1.mask,can1.intr);
    Drv_Can_Init(can2.Can_no,can2.baudrate,can2.id,can2.mask,can2.intr);
    Drv_Can_Init(can3.Can_no,can3.baudrate,can3.id,can3.mask,can3.intr);
    
    uint8_t buffer_right1[]={//8个,地址为操作模式下的16~21(注意这里是颠倒配置的)
    //ID28~ID21
    0xff,
    //0(标准) RTR X X DLC3 DLC2 DLC1 DLC0 (X一般写0):1000 0011(DLC=3);也代表ID20~18=000
    0x03,
    //19.20.21数据
    0x23,0x24,'C'//数据
    };

    uint8_t buffer_right2[]={//8个,地址为操作模式下的16~21(注意这里是颠倒配置的)
    //ID28~ID21
    0xff,
    //0(标准) RTR X X DLC3 DLC2 DLC1 DLC0 (X一般写0):1000 0011(DLC=3);也代表ID20~18=000
    0x03,
    //19.20.21数据
    0x23,0x24,'D'//数据
    };

    uint8_t buffer_right3[]={//8个,地址为操作模式下的16~21(注意这里是颠倒配置的)
    //ID28~ID21
    0xff,
    //0(标准) RTR X X DLC3 DLC2 DLC1 DLC0 (X一般写0):1000 0011(DLC=3);也代表ID20~18=000
    0x03,
    //19.20.21数据
    0x23,0x24,'E'//数据
    };

    char buffer_recv[8];
    int len;

    //CAN0发送数据
    Drv_Can_Send(can0.Can_no,&buffer_right1);

    print2("=========receive============\r\n");
    len = Drv_Can_Recv(can1.Can_no,&buffer_recv);
    print2("len1 = %d\r\n",len);

    //Drv_Can_Send(can1.Can_no,&buffer_right1);
    print2("=========receive============\r\n");
    len = Drv_Can_Recv(can2.Can_no,&buffer_recv);
    print2("len2 = %d\r\n",len);

    //Drv_Can_Send(can1.Can_no,&buffer_right1);
    print2("=========receive============\r\n");
    len = Drv_Can_Recv(can3.Can_no,&buffer_recv);
    print2("len3 = %d\r\n",len);


}

int main(){
    gic_init(); 
    //以下测试在QEMU端可见，未使用502驱动函数；最后一项测试针对502驱动函数，串口助手可见
    //single_filter_extended_test();//单过滤+拓展帧测试
    //single_filter_standard_test();//单过滤+标准帧测试
    //dual_filter_extended_test();//双过滤+拓展帧测试
    dual_filter_standard_test();//双过滤+标准帧测试
    //sleep_mode_test();//睡眠模式（初步）测试
    //selftest_mode_test();//自测模式测试
    //ListenOnly_mode_test();//监听模式测试
    //RI_test();//接收中断测试
    //TI_test();//发送中断测试
    //DOI_test();//数据溢出中断测试
    //WUI_test();//唤醒中断测试程度
    //以下错误注入需要QEMU段的配置
    //inject96_test();//接收端错误注入测试（96）
    //inject127_test();//接收端错误注入测试（127）
    //inject255_test();//接收端错误注入测试（255）
    //DRV_CAN_test();//测试使用驱动函数
}
