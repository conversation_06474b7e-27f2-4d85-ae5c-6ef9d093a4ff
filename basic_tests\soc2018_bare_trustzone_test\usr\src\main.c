/*
 * 适用于 soc2018 的 TrustZone 测试程序
 */

#include "types.h"
#include "gic.h"
#include "secure_util.h"
#include <stdbool.h>

extern int irq_count;

int main(void)
{
    /* GIC 初始化 */
    gic_init();

    /* 业务代码开始 */
    bool ns_flag = is_ns(); /* ns_flag == 0 */
    switch_to_ns();
    // ns_flag = is_ns();   /* Undefined_Handler */
    switch_to_s();
    switch_to_ns();
    switch_to_s();
    /* 业务代码结束 */

    return 0;
}
