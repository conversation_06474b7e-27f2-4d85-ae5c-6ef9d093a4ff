/*
 * 此处填写用户程序业务逻辑描述
 */
#include "types.h"
#include "gic.h"
#include "sp804.h"

extern int irq_count;

int main(void)
{
    /* GIC 初始化 */
    gic_init();
    
    /* 在此处撰写业务代码 */
    // 设置 SP804
    WRITE_32(TIMER_LOAD, 1000000);                                       // 加载计数值为 1000000 （1s 触发一次中断） sp804 的频率为 1MHz 即 1us 计数一次
    WRITE_32(TIMER_CTRL, (1 << 7) | (1 << 6) | (1 << 5) | (0 << 2));     // 定时器使能、周期模式、中断使能、分频系数 1

    while (irq_count < 5) {
        // 等待5次中断触发
    } 
    
    return 0;
}
