# QEMU虚拟机用户程序代码

## 仓库说明
本仓库根目录下的各子目录均代表一套可被编译和运行的用户程序代码。其中，soc2018_bare_xxx 为适用于 soc2018 裸机运行的用户代码模板，开发者后续可基于此模板进行开发工作。

## 注意
若无特别说明，本 README 中所有被花括号“{}”包围的内容均为占位符，请在阅读和使用时替换为对应值。

## 用户程序代码开发环境配置教程

1. **克隆仓库**：
    - 在浏览器中 Fork 本仓库。
    - 在 MSYS2 环境中，进入到你希望存放此仓库的父级目录中，使用以下命令克隆你的 Fork 仓库：
      ```PowerShell
      git clone https://gitee.com/{user_name}/qemu_app.git
      ```

2. **完成克隆**：
    - 克隆完成后，即可直接使用或进行再开发。

## 编译说明

1. **修改 Makefile 中编译器配置**
    - 在 VS Code 中，修改要使用的 Makefile 的 GCC_HOME 一项，将其设置为本机 arm-none-eabi-gcc 工具链所在目录：
    ```
    GCC_HOME = {工具链绝对路径}
    ```

2. **进入待编译的用户程序目录**：
    ```PowerShell
    cd qemu_app/{app_dir}
    ```

3. **执行 Make 指令**：
    ```PowerShell
    make
    ```

4. **编译完成**：
    - Make 执行成功后，相应的`{app_dir}` 目录下会产生 `build` 目录以及相应的可执行文件。

5. **运行用户程序**：
    - 将用户程序复制至 `qemu-system-arm.exe` 所在目录下，在 MSYS2 环境运行中使用正确的命令运行虚拟机即可。

6. **清理用户程序**：
    - 进入相应的`{app_dir}` 目录下，执行以下命令即可清理构建内容和用户程序：
    ```PowerShell
    make clean
    ```

## 协作开发步骤

1. **Fork 本仓库并克隆到本地**

2. **配置个人信息**
    ```bash
    git config --global user.name 'San Zhang'
    git config --global user.email '<EMAIL>'
    ```

3. **新建 develop 分支**：
    ```bash
    git checkout -b develop/{feature_name}
    ```

4. **提交代码**：
    - 在新分支上进行开发，完成后提交代码。

5. **创建 Pull Request**：
    - 将分支推送到你的 Fork 仓库，并在 Gitee 上创建 Pull Request。
