#include "privtimers.h"
#include "gic.h"
#include "types.h"

int global_timer_irq_count = 0;
int private_timer_irq_count = 0;
int watchdog_timer_irq_count = 0;

/*
 * Drivers
 */
uint64_t global_timer_read(void) {
    uint32_t high1, low, high2;
    
    do {
        high1 = READ_32(GLOBAL_TIMER_COUNTER_HIGH);
        low   = READ_32(GLOBAL_TIMER_COUNTER_LOW);
        high2 = READ_32(GLOBAL_TIMER_COUNTER_HIGH);
    } while (high1 != high2);
    
    return ((uint64_t)high1 << 32) | low;
}

void global_timer_delay_us(uint32_t us) {
    const uint64_t ticks_per_us = 1000;  // 1 tick = 1ns → 1us = 1000 ticks
    uint64_t start = global_timer_read();
    uint64_t delay_ticks = (uint64_t)us * ticks_per_us;
    
    while ((global_timer_read() - start) < delay_ticks) {
        // 空等待
    }
}

/*
 * ISRs
 */
void global_timer_isr(void) {
    // 读中断状态寄存器用于调试
    uint32_t int_status = READ_32(GLOBAL_TIMER_ISR);
    uint32_t ctlr = READ_32(GLOBAL_TIMER_CTLR);

    // 写中断状态寄存器
    WRITE_32(GLOBAL_TIMER_ISR, 0x1);

    global_timer_irq_count++;
}

// 中断服务程序（需在GIC中注册）
void private_timer_isr(void) 
{
    // 写中断状态寄存器
    WRITE_32(PRIVATE_TIMER_ISR, 0x1);
    
    private_timer_irq_count++;
}

void watchdog_timer_isr(void) 
{
    // 清除中断标志
    WRITE_32(WDT_ISR, 0x1);
    
    watchdog_timer_irq_count++;
}

/*
 * 测试代码
 */
/* 全局定时器计数器功能测试 - 不会触发中断，计数器可正常工作 */
void global_timer_count_test(void) {
    uint64_t timestamp;

    // 重置控制寄存器
    WRITE_32(GLOBAL_TIMER_CTLR, 0);
    
    // 清除计数器
    WRITE_32(GLOBAL_TIMER_COUNTER_LOW, 0);
    WRITE_32(GLOBAL_TIMER_COUNTER_HIGH, 0);
    
    // 禁用自动递增
    WRITE_32(GLOBAL_TIMER_AUTO_INCR, 0);
    
    // 启用定时器、中断
    WRITE_32(GLOBAL_TIMER_CTLR, (1 << 0) | (1 << 2));

    while (1) {
        // 记录时间戳
        timestamp = global_timer_read();
        
        // 延时500ms
        global_timer_delay_us(500000);
        
        // 计算实际耗时（调试用）
        uint64_t elapsed = global_timer_read() - timestamp; // 近似为 500000
    }
}

/* 全局定时器中断功能测试 - 会触发中断，计数器可正常工作 */
void global_timer_compare_test(void) {
    // 重置控制寄存器
    WRITE_32(GLOBAL_TIMER_CTLR, 0);

    // 清除计数器（确保从0开始计数）
    WRITE_32(GLOBAL_TIMER_COUNTER_LOW, 0);
    WRITE_32(GLOBAL_TIMER_COUNTER_HIGH, 0);

    // 禁用自动递增（若需自动递增，需设置步长）
    WRITE_32(GLOBAL_TIMER_AUTO_INCR, 0);

    // 设置比较值
    uint64_t compare_value = 100000000;
    WRITE_32(GLOBAL_TIMER_CMP_LOW, (uint32_t)compare_value);
    WRITE_32(GLOBAL_TIMER_CMP_HIGH, (uint32_t)(compare_value >> 32));

    // 自动递增设置
    WRITE_32(GLOBAL_TIMER_AUTO_INCR, 100000000);

    // 启用定时器、比较器、中断、自动递增
    WRITE_32(GLOBAL_TIMER_CTLR, (1 << 0) | (1 << 1) | (1 << 2) | (1 << 3));

    while (global_timer_irq_count < 5) {
    
    }

    // 重置控制寄存器
    WRITE_32(GLOBAL_TIMER_CTLR, 0x00000000);
}

/* 私有定时器计数器功能测试 */
void private_timer_count_test(void) 
{
    uint32_t initial_count;
    
    // 重置控制寄存器
    WRITE_32(PRIVATE_TIMER_CTLR, 0x00000000);
    
    // 设置加载值
    uint32_t load_value = 200000000; 
    WRITE_32(PRIVATE_TIMER_LOAD, load_value);
    
    // 仅启用定时器
    WRITE_32(PRIVATE_TIMER_CTLR, (1 << 0));
    
    // 读取初始计数值 - 略小于加载值
    initial_count = READ_32(PRIVATE_TIMER_COUNTER);
    while (initial_count > 0) {
        // 持续轮询计数值递减
        uint32_t current_count = READ_32(PRIVATE_TIMER_COUNTER);
        if (current_count < initial_count) {
            initial_count = current_count;
        }
    }
    
    // 停止定时器
    WRITE_32(PRIVATE_TIMER_CTLR, 0x00000000);
}

/* 私有定时器中断及自动加载功能测试 */
void private_timer_load_test(void) 
{
    // 停止定时器并重置控制寄存器
    WRITE_32(PRIVATE_TIMER_CTLR, 0x00000000);
    
    // 设置加载值
    uint32_t load_value = 1000000;
    WRITE_32(PRIVATE_TIMER_LOAD, load_value);
    
    // 启用定时器、自动重载、中断
    WRITE_32(PRIVATE_TIMER_CTLR, (1 << 0) | (1 << 1) | (1 << 2)); // EN=1, IRQ_EN=1, AUTO=1
    
    // 等待5次中断触发
    while (private_timer_irq_count < 5) {

    }
    
    // 停止定时器
    WRITE_32(PRIVATE_TIMER_CTLR, 0x00000000);
}

/* WDT 计数器功能 - 同私有计时器 */
void watchdog_count_test(void) 
{
    // 重置控制寄存器
    WRITE_32(WDT_CTRL, 0x00000000);
    
    // 设置初始加载值
    uint32_t load_value = 1000000;
    WRITE_32(WDT_LOAD, load_value);
    
    // 计数器功能
    WRITE_32(WDT_CTRL, (1 << 0));
    
    // 监控计数器递减
    uint32_t timeout = 0;
    while (timeout++ < 3) {
        uint32_t current = READ_32(WDT_COUNTER);
        global_timer_delay_us(1000000); 
    }
    
    // 停止看门狗
    WRITE_32(WDT_CTRL, 0x00000000);
}

/* WDT 计时器中断测试 - 同私有计时器 */
void watchdog_irq_test(void) 
{
    // 重置控制寄存器
    WRITE_32(WDT_CTRL, 0x00000000);
    
    // 设置加载值
    uint32_t load_value = 2000000;
    WRITE_32(WDT_LOAD, load_value);
    
    // 启用 WDT、中断模式
    WRITE_32(WDT_CTRL, (1 << 0) | (1 << 2));

    uint32_t ctrl = READ_32(WDT_CTRL);
    
    ctrl = READ_32(WDT_CTRL);

    // 等待3次中断触发（ISR中自动喂狗）
    while (watchdog_timer_irq_count < 3) {
    
    }
    
    // 停止看门狗
    WRITE_32(WDT_CTRL, 0x00000000);
}

/* 复位模式测试 - 未实现 */
void watchdog_reset_test(void) 
{
    // 重置控制寄存器
    WRITE_32(WDT_CTRL, 0x00000000);
    
    // 设置加载值
    uint32_t load_value = 100000000;
    WRITE_32(WDT_LOAD, load_value);
    
    // 启用 WDT、复位模式
    WRITE_32(WDT_CTRL, (1 << 0) | (1 << 3));
    
    while (1) {
    
    }
}
