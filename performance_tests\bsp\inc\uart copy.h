#ifndef _UART_H
#define _UART_H

//uart_initialize(u32 base_addr, u32 devisor);

//#endif /* _UART_H */



#include "types.h"

void uart_initialize(u32 base_addr, u32 devisor);
void uart_init_all(void);
void uart_out_byte(u32 base_addr, u8 byte);
u32  uart_in_byte(u32 base_addr);
void uart_irq_handle(u32 irq_num);

unsigned int MoniterRead(void);
void MoniterWrite(unsigned int sendchar);
void MoniterWriteBlock(unsigned int *_sendchar, unsigned int _sendNo);

#endif /* _UART_H */
