#include "bsp_print.h"

void printn(U32 n,U32 b)
{
    U32 a;
    if ( (a = (n/b)) != 0 )	/*根据数值计算输出数据*/
    {
        printn(a,b);	/*采用递归方式输出数据*/
    }

    uart_putc( charlist[n%b] );
}

void printn_long(U64 n,U64 b)
{
    U64 a;
    if ( (a = (n/b)) != 0 )	/*根据数值计算输出数据*/
    {
        printn_long(a,b);	/*采用递归方式输出数据*/
    }

    uart_putc( charlist[n%b] );
}

void printm_x_10(U64 nx,U32 kp, U32 shw)
	{
		U32 i,j;
		U64 x;
		U32 key,sign_jin,sign_yu;
		U32 Sum_array[100],F_array[100];
		V_U32 temp;

		x = nx;
		
		for (i = 0; i < kp; i++)
		{
				F_array[i] = 0;
				Sum_array[i] = 0; 	
		}
		
		F_array[0] = 5;	/*初始化第1位二进制小数位的权值*/
		sign_jin = 0;		/*进位标志清0*/
		
		for (i = kp; i > 0; i--)
		{
			if ((i-1) >= 64)	/*避免移位卷回造成错误*/
			{
				key = 0;
			}else
			{
				key = (x >> (i-1)) & 0x1;	/*获取1位二进制小数数字*/
			}
			
			if (key == 1)
			{
				sign_jin = 0;
				for( j = kp; j > 0; j--)
				{
					Sum_array[j-1] = Sum_array[j-1] + F_array[ j - 1] + sign_jin;/*进行十进制小数部分叠加*/

					if (Sum_array[j-1] > 9)/*处理进位问题*/
					{
						Sum_array[j-1] = Sum_array[j-1] - 10;
						sign_jin = 1;	
					}else
					{
						sign_jin = 0;	
					}
				}	
			}
			
			sign_yu = 0;	/*余数清0*/
			for( j = 0; j < kp; j++)		/*计算下一位小数数字的权值*/
			{
				F_array[j] = F_array[j] + sign_yu * 10;
				temp = F_array[j] >> 1;
				sign_yu = F_array[j] - (temp << 1);	
				F_array[j] = temp;
			}
		}

		for (i = 0; i < shw; i++)/*按十进制输出小数部分*/
		{
			printn(Sum_array[i],10);	
		}
	}

	void print_f32(U32 tn_32, U8 c_8)
	{
		U32 tn;
		U8 c;
		U8 sign_m;
		U8 sign_e;
		U32 e;
		U32 m;
		U32 z;
		U32 x;
		U32 keep;
		U32 show;
		U32 f8 = 0xffffffff;

		tn = tn_32;
		c = c_8;
	
		if ( (tn & 0x80000000) == 0x80000000 )	/*判断数据是否为负数*/
		{
			uart_putc('-');			/*打印输出负号*/
			tn = tn & 0x7fffffff;
			sign_m = 0;
		}else
		{
			//uart_putc('+');			/*打印输出正号*/
			sign_m = 1;
		}

		if (c == 'e')
		{
			uart_putc('[');
			uart_putc('2');
			uart_putc('e');
		}

		e = (tn & 0x7f800000) >> 23;		/*bit30~bit23 get E*/

		if (e>=127)	/* e is + */
		{
			e=e-127;
			sign_e = 1;
			if (c == 'e')
			{
				uart_putc('+');
				printn(e,10);
				uart_putc(']');
			}
		}else	/* e is - */
		{
			e=127-e;
			sign_e = 0;
			if (c == 'e')
			{
				uart_putc('-');
				printn(e,10);
				uart_putc(']');
			}
		}

		m = tn & 0x007fffff;	/*bit22~bit0 get m*/
		
		if (c == 'e')
		{
				uart_putc('[');
				uart_putc('1');
				uart_putc('.');
				printn(m,2u);
				uart_putc('b');
				uart_putc(']');
		}
						
		m = m | 0x00800000;			//bit23 set 1

		if ((c == 'f') || (c == 'm'))
		{
			if (sign_e == 1)
			{
				/*向右移动小数点*/
				if (e <= 23)/*移位量必须是正数*/
				{				
					z = m >> (23 - e);
					printn(z, 10);	/*打印输出整数部分*/
					uart_putc('.');
					
					keep = 23 - e;
					x = m & (f8 >> (9 + e));
				}else
				{
					z = m << (e-23);
					printn(z, 10);	/*打印输出整数部分*/
					uart_putc('.');
					uart_putc('0');

					return;
				}
			}else
			{
				/*向左移动小数点*/
				uart_putc('0');
				uart_putc('.');
				
				keep = 23 + e;
				x = m;
			}
			
			if (e == 127)
			{
				uart_putc('0');
			}else
			{
				if (c == 'f')
				{
					show = NUM_FACTION;
				}

				if (c == 'm')
				{
					show = keep;
				}
				printm_x_10((U64)x,keep,show);
			}
		}	
			

	}

	void print_f64(U64 tm_64, U8 c_8)
	{
		U64 tm;
		U8 c;
		U8 sign_m;
		U8 sign_e;
		U32 e;
		U64 m64;
		U64 z64;
		U64 x64;
		U32 keep;
		U32 show;
		U64 f16 = 0xffffffffffffffff;

		tm = tm_64;
		c = c_8;
	
		if ( (tm & 0x8000000000000000) == 0x8000000000000000 )	/*打印数据，判断数据是否为负数*/
		{
			uart_putc('-');			/*打印输出负号*/
			tm = tm & 0x7fffffffffffffff;
			sign_m = 0;
		}else
		{
			//uart_putc('+');
			sign_m = 1;
		}
		
		if (c == 'E' || c == 'e')	/*用科学计数法输出*/
		{
			uart_putc('[');
			uart_putc('2');
			uart_putc('e');
		}

        // 取出指数部分
		e = (tm & 0x7ff0000000000000) >> 52;		/*bit62~bit52 get E*/
        
		if (e >= 1023)	/* e is + */
		{
			e = e - 1023;
			sign_e = 1;
			
			if (c == 'E' || c == 'e')
			{
				uart_putc('+');
				printn(e,10);
				uart_putc(']');
			}
		}else	/* e is - */
		{
			e = 1023 - e;
			sign_e = 0;
			if (c == 'E' || c == 'e')
			{
				uart_putc('-');
				printn(e,10);
				uart_putc(']');
			}
		}

		// 取出尾数部分
		m64 = tm & 0x000fffffffffffff ;	/*bit51~bit0 get m*/
		
		if (c == 'E' || c == 'e')
		{
				uart_putc('[');
				uart_putc('1');
				uart_putc('.');
				printn_long(m64,2u);
				uart_putc('b');
				uart_putc(']');
		}

        // 尾数部分加上隐藏位
		m64 = m64 |0x0010000000000000;	//bit52 set 1
		
		if ((c == 'F') || (c == 'M') || (c == 'f') || (c == 'm'))
		{	
			if (sign_e == 1)
			{
				/*向右移动小数点*/
				if (e <= 52)/*移位量必须是正数*/
				{
					z64 = m64 >> (52 - e);
					printn_long(z64, 10);	/*打印输出数字*/
					uart_putc('.');
					
					keep = 52 - e;
					x64 = m64 & (f16 >> (12 + e));
				}else
				{
					z64 = m64 << (e-52);
					printn_long(z64, 10);	/*打印输出整数部分*/
					uart_putc('.');
					uart_putc('0');

					return;
				}
			}else
			{
				/*向左移动小数点*/
				uart_putc('0');
				uart_putc('.');
				
				keep = 52 + e;
				x64 = m64;
			}

			if (e == 1023)
			{
				uart_putc('0');
			}else
			{
				if (c == 'F' || c == 'f')
				{
					show = NUM_FACTION;
				}
				
				if (c == 'M' || c == 'm')
				{
					show = keep;
				}
				printm_x_10(x64,keep,show);
			}
		}
	}
    
/* 
 ****************************************************************************
 * 
 * 名称：print2
 *
 * 功能：串口数据输出。
 *
 * 调用：无
 * 
 * 被调用: 无
 * 
 * 输入：无
 * 
 * 输出：无
 *
 * 返回值: 无
 *
 * 使用约束：
 *
 * *****************************************************************************
*/
void print2(char fmt[],...)
{
    U8 *s,c;
    U32 tn;
    U64 tm;
    V_F32 tn_F32;
    
    va_list var_arg;
    //uart_putc(0xAA);
    //while(1);
    va_start(var_arg,fmt);	/*获取参数*/

    for(;;)
    {
        while ((c = (*fmt++)) != '%')	/*判断是否遇到%*/
        {
            if (c == 0)	/*字符串输出完成*/
            {
                va_end(var_arg);
                return;		/*返回*/
            }
            uart_putc(c);		/*输出普通的字符串*/
        }
        
        c = *fmt++;
        
        if ( (c == 'd') || (c == 'i') )	/*读取字符串是遇到%，判断%后紧跟着是否为d、i，即为有符号数*/
        {
            tn = va_arg(var_arg,U32);	/*获取参数个数*/
            
            if ( ((int)tn) < 0 )	/*打印数据，判断数据是否为负数*/
            {
                uart_putc('-');			/*打印输出负号*/
                tn = 0-((U32)tn);
            }
            printn(tn, 10);	/*打印输出数字*/
        }
        else if ( (c == 'o') || (c == 'u') || (c == 'x') )	/*读取字符串是遇到%，判断%后紧跟着是否为o、u、x，即为无符号数*/
        {
            tn = va_arg(var_arg,U32);	/*获取参数个数*/
            
            printn(tn, (c == 'o')?8u:((c == 'u')?10u:16u));	/*根据期望的数值打印输出数字*/
                
        }
		else if (c == 'p')	/*print pointer*/
		{
			tn = va_arg(var_arg,U32);	/*获取参数个数*/
			uart_putc('0');
			uart_putc('x');
			printn(tn, 16u);	/*打印输出数字*/
		}
		else if (c == 'X')	/*print U64*/
        {
            tm = va_arg(var_arg,U64);	/*获取参数个数*/

            // #ifdef __DOUBLE__
			printn_long(tm, 16u);	/*打印输出数字*/
            // #endif
            
        }
        else if ( (c == 'f') || (c == 'e') || (c == 'm') )/*print F32*/
        {	
            //tn_F32 = va_arg(var_arg,F32);	/*获取参数个数*/
            //tn = (U32)(*((U32 *)(&tn_F32)));
            
            // tn = va_arg(var_arg,U32);	/*获取参数个数*/
            tm = va_arg(var_arg,U64);	/*获取参数个数*/

        #if 0
            uart_putc('t');
            uart_putc('n');
            uart_putc('=');
            printn(tn, 16u);	/*打印输出数字*/
            uart_putc(' ');		
        #endif
        
            #ifdef __FLOAT__
                // print_f32(tn, c);
                print_f64(tm, c);
            #endif
                
        }else if ( (c == 'F') || (c == 'E') || (c == 'M') )/*print F64*/
        {
            tm = va_arg(var_arg,U64);	/*获取参数个数*/

            #ifdef __FLOAT__
                print_f64(tm, c);
            #endif
            
        }else if (c == 'c')
        {
            tn = va_arg(var_arg,U32);	/*获取参数个数*/
            // s = (U8 *)tn;
            uart_putc((int)tn);			/*输出普通的单个字符*/
        }
        else
        {
            tn = va_arg(var_arg,U32);	/*获取参数个数*/
            
            if (c == 's')	/*读取字符串是遇到%，判断%后紧跟着是否为s,即要输出字符串*/
            {
                s = (U8 *)tn;
                while ( c = *s++ )	/*依次输出字符串*/
                {
                    uart_putc(c);
                }
            }
        }
        
    }
}