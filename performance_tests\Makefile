# -------------------------------------------------------------------
# 工具链配置 (请自行修改 GCC_HOME)
# -------------------------------------------------------------------
GCC_HOME  = 
CC        = arm-none-eabi-gcc
AS        = arm-none-eabi-gcc
LD        = arm-none-eabi-gcc
OBJDUMP   = arm-none-eabi-objdump
OBJCOPY   = arm-none-eabi-objcopy
READELF   = arm-none-eabi-readelf

# -------------------------------------------------------------------
# 编译器、汇编器、链接器选项
# -------------------------------------------------------------------
CFLAGS    = -c -g -O0 -mfloat-abi=hard -mfpu=neon-vfpv3 -mcpu=cortex-a9
ASFLAGS   = -c -g -mfloat-abi=hard -mfpu=neon-vfpv3 -mcpu=cortex-a9
LDFLAGS   = -g -nostartfiles
LDFLAGS  += -lm

# 标准库链接路径（按需修改）
GCC_LIB  += -L $(GCC_HOME)/lib/gcc/arm-none-eabi/9.2.1/arm/v5te/hard
GCC_LIB  += -L $(GCC_HOME)/arm-none-eabi/lib/arm/v5te/hard
GCC_LIB  += -L $(GCC_HOME)/arm-none-eabi/lib
GCC_LIB  += -L .

# 链接时附加参数
LINK      = $(LDFLAGS) -T link_script.ld $(GCC_LIB)

# -------------------------------------------------------------------
# 模块列表 —— 新增模块只需在这里添加一行
# -------------------------------------------------------------------
MODULES   := bsp               \
             usr        

# 根据 MODULES 自动生成 include 和 src 路径
INCDIRS   := $(addsuffix /inc, $(MODULES))
SRCDIRS   := . $(addsuffix /src, $(MODULES))

# -------------------------------------------------------------------
# 文件搜索与对象列表生成
# -------------------------------------------------------------------
ASFILES     := $(foreach dir,$(SRCDIRS),$(wildcard $(dir)/*.S))
CFILES      := $(foreach dir,$(SRCDIRS),$(wildcard $(dir)/*.c))

ASFILENDIR  := $(notdir $(ASFILES))
CFILENDIR   := $(notdir $(CFILES))

INCLUDE     := $(patsubst %,-I%,$(INCDIRS))

OBJDIR      := build
ASOBJS      := $(patsubst %.S,$(OBJDIR)/%.o,$(ASFILENDIR))
COBJS       := $(patsubst %.c,$(OBJDIR)/%.o,$(CFILENDIR))
OBJS        := $(ASOBJS) $(COBJS)

# VPATH 让 make 在这些目录里找源文件
VPATH       := $(SRCDIRS)

# -------------------------------------------------------------------
# 构建规则
# -------------------------------------------------------------------
TARGET      := app.elf

.PHONY: all clean show

# 默认目标：编译并链接
all: $(TARGET)

# 链接所有中间文件
$(TARGET): $(OBJS)
	$(LD) $^ -o $@ $(LINK)

# 汇编 .S -> .o
$(OBJDIR)/%.o: %.S | $(OBJDIR)
	$(AS) $(ASFLAGS) $(INCLUDE) -o $@ $<

# 编译 .c -> .o
$(OBJDIR)/%.o: %.c | $(OBJDIR)
	$(CC) $(CFLAGS) $(INCLUDE) -o $@ $<

# 自动创建 build 目录
$(OBJDIR):
	mkdir $(OBJDIR)

# 清理
clean:
	@if exist $(OBJDIR) rmdir /S /Q $(OBJDIR)
	@if exist $(TARGET) del $(TARGET)
	@echo Cleaning complete: $(OBJDIR) and $(TARGET) removed.

# 打印变量调试
show:
	@echo "MODULES  = $(MODULES)"
	@echo "INCDIRS  = $(INCDIRS)"
	@echo "SRCDIRS  = $(SRCDIRS)"
	@echo "ASFILES  = $(ASFILES)"
	@echo "CFILES   = $(CFILES)"
	@echo "OBJS     = $(OBJS)"