
#include "soc502_parameter.h"
#include "types.h"
#include "uart.h"

#define rd(offset)                r32(APB_UART0_BASE + offset)
#define wr(offset, val)           w32(APB_UART0_BASE + offset, val)


//////////////////////////////////////////////
// basic functions
/////////////////////////////////////////////
void uart_initialize(u32 base_addr, u32 devisor)
{
	// set baud rate
	w32(base_addr + 0x0c, 0x83);
	w32(base_addr + 0x00, devisor & 0xff);
	w32(base_addr + 0x04, devisor >> 8);
	w32(base_addr + 0x0c, 0x03);
	// enable tx empty interrput
	w32(base_addr + 0x04, 0x02);
}

void uart_init_all(void)
{
	uart_initialize(APB_UART0_BASE, 0x0001);
	uart_initialize(APB_UART1_BASE, 0x0001);
	uart_initialize(APB_UART2_BASE, 0x0001);
	uart_initialize(APB_UART3_BASE, 0x0001);
}

void uart_out_byte(u32 base_addr, u8 byte)
{
	while( !(r32(base_addr + 0x14) & 0x20) )
		;
	w32(base_addr + 0x00, byte);
}

u32 uart_in_byte(u32 base_addr)
{
	while( !(r32(base_addr + 0x14) & 0x01) )
		;
	return r32(base_addr + 0x00);
}

void uart_irq_handle(u32 irq_num)
{
	u32 base_addr;
	u32 temp;

	//mprintf("uart ");
	//printnum(irq_num);
	//mprintf(" irq handle\n");
	base_addr = APB_UART0_BASE + 0x100 * (irq_num - 32);

	temp = r32(base_addr + 0x08);
	//printval("IIR", temp);
	temp = r32(base_addr + 0x14);
}

