/*
* ****************************************************************************
* 文件名:	BSPWDT.h
* 创建日期:
* 版本:
* 功能:需要用户修改的BSP宏定义放在这个文件中
*
* ****************************************************************************
*/

#ifndef __BSP_CPU_WDT_H__
#define __BSP_CPU_WDT_H__

#include "types.h"

#define BSP_WDT_RELOAD_OFFSET_REG			(0x20)
#define BSP_WDT_COUNT_OFFSET_REG			(0x24)
#define BSP_WDT_CONTROL_OFFSET_REG			(0x28)
#define BSP_WDT_INT_STATUS_OFFSET_REG		(0x2C)
#define BSP_WDT_RESET_STATUS_OFFSET_REG		(0x30)
#define BSP_WDT_FORBIDDEN_OFFSET_REG		(0x34)

/*处理器时钟频率配置,单位MHz*/
#define CPU_FREQ				400

#define BSP_WDT_TIMER_FREQ					(CPU_FREQ)

#define	WTDTIMER_FREQ						(CPU_FREQ)		/* Private Timers Freq*/
#define	WTDTIMER_FREQ_DIVIDED_PARAM			(2)				/*Select from:0;1;2*/

#define BSP_AMBA_WDT_BASEADDR		(0x3FFF0600)					/*看门狗定时器寄存器基地址-BSPMECBASE*/

#define BSP_WDT_IRQID				(30)		/* Irq id of WDT Timer*/

/*----------------------内狗相关配置----------------------*/
/*A.设置看门狗复位时间(本时间内不清狗则内部看门狗复位), 单位us, 不可大于 (TIMER_SCALER*0xffffffff)/1000 
 *若不使用内狗(不使用启动内狗及清内狗宏)，则无需设置此值。当量us
*/
#define WDT_RESET_TIME				(4000000) 
/* 修改缩短时间 */
//#define WDT_RESET_TIME				(4000) 
#ifndef __ASMLANG__
/*函数声明*/
void Bsp_CpuWdtEnable(void);
void Bsp_CpuWdtDisable(void);
void Bsp_CpuWdtClear(U32 wdtTime);
void Bsp_CpuWdtSet(U32 wdtTime);
U32 Bsp_CpuWdtGetResetStatus(void);
void Bsp_CpuWdtClearResetStatus(void);
void Bsp_CpuWdtStop(void);
void Bsp_CpuWdtModeSet(void);
#endif
#endif /* __BSP_CPU_WDT_H__ */

