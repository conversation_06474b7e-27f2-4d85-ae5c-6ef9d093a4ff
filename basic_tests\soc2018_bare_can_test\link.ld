/* 定义内存起始地址、大小以及堆栈最小尺寸 */
_RAM_START = 0x10000000;
_RAM_SIZE  = 8M;

_Min_Heap_Size  = 0x200;
_Min_Stack_Size = 0x400;

MEMORY
{
  ram : ORIGIN = _RAM_START, LENGTH = _RAM_SIZE
}

SECTIONS
{
  /* 异常向量表放在RAM的起始地址，即0x10000000 */
  .vectors :
  {
    . = ORIGIN(ram);
    __exception_vector_start = .;
    KEEP(*(.vectors))
    . = ALIGN(32);
  } > ram

  .text : 
  {
    *(.text)
    *(.text.*)

    . = ALIGN(16);
    *(.eh_frame)

    . = ALIGN(16);
    *(.rodata*)
    *(.note.gnu.build-id)

    . = ALIGN(16);
    *(.init)
    *(.fini)
    *(.lit)
    *(.shdata)
  } > ram

  .mmudata :
  {
    . = ALIGN(16);
    *(.mmudata*)
  } > ram

  .data : 
  {
    . = ALIGN(16);
    *(.data)
    *(.data.*)
  } > ram

  .bss :
  {
    __bss_start = ALIGN(8);
    *(.bss)
    *(.bss*)
    *(COMMON)
    . = ALIGN(8);
    __bss_end = .;
  } > ram

  /* 堆与栈预留区域 */
  .something :
  {
    . = ALIGN(8);
    . = . + _Min_Heap_Size;
    . = . + _Min_Stack_Size;
    . = ALIGN(8);
  } > ram
}
