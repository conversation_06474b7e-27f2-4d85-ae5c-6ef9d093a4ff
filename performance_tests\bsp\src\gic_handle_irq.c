
#include "soc502_parameter.h"
#include "types.h"
#include "irq_gic.h"
#include "uart.h"
#include "apb_timer.h"
//#include "sdram.h"
//#include "emif.h"

unsigned int gic_get_irq_number(void);
void gic_eoi_irq(unsigned int irq);
void printval(const charptr name, unsigned int val);
u32 i=0;
u32 irq_num;
u32 fiq_num;
u32 rdata;
unsigned int rdata2;
void gic_handle_fiq(void)
{
    fiq_num=gic_get_irq_number();
    mprintf("!-FIQ Interrupt Acknowledged-! \n");
   // printval("1 interrupt number is ",irq_num);
    mprintf("FIQ interrupt number is %d \n", fiq_num);
    if(fiq_num==40)//15531中断
	{
//	   mprintf("RT ISR is 0x%x\n",D2_Reg_Read(6));
 //      D2_Reg_write(3,0x0004);//RT中断寄存器清零 
//	   D2_RAM_write(0xffff,0x1234);//
	}
	 else if(fiq_num==43)//15530中断
	{
//	   mprintf("BC ISR is 0x%x\n",D1_Reg_Read(6));
//	   D1_Reg_write(3,0x0004);//BC中断状态寄存器清零
//	   D1_RAM_write(0xefb,0x1234);//
	}

 else if(fiq_num==32)//片上SRAM单错中断
	{
	   rdata = *(volatile unsigned int *)(0x40200018);
	   mprintf("sig err total num  is %d\n",rdata);
	   rdata = *(volatile unsigned int *)(0x40200040);
	   mprintf("sig err byte is 0x%X \n",rdata); 
	   rdata = *(volatile unsigned int *)(0x40200010);
	   mprintf("sig err addr is 0x%x\n",rdata); 
	   
	   if(*(volatile unsigned int *)(0x40200040)>=0x1000)
				{
					rdata2 = r32(rdata+0x4);
					*(volatile unsigned int *)(rdata+0x4) = rdata2;}//edac_data
	   else
	   		{
	   			rdata2 = r32(rdata);
	   			*(volatile unsigned int *)(rdata) = rdata2;}//edac_data
	   
	   
	   *(volatile unsigned int *)(0x40200030) = 0x0;//BC中断状态寄存器清零
	  
	}
else if(fiq_num==33)//片上SRAMshuang 错中断
	{
	   rdata = *(volatile unsigned int *)(0x40200040);
	   mprintf("sig err byte is 0x%X \n",rdata); 
	   rdata = *(volatile unsigned int *)(0x40200020);
	   mprintf("double err addr is 0x%x\n",rdata); 
		  rdata2 = *(volatile unsigned int *)(0x40200058);
	   	mprintf("error edac low code is 0x%x \n",rdata2);
	   	rdata2 = *(volatile unsigned int *)(0x4020005c);
	   	mprintf("error edac high code is 0x%x \n",rdata2);
	   	rdata2 = *(volatile unsigned int *)(0x40200060);
	   	mprintf("error data low code is 0x%x \n",rdata2);
	   	rdata2 = *(volatile unsigned int *)(0x4020006c);
	   	mprintf("error data high code is 0x%x \n",rdata2);
	   *(volatile unsigned int *)(0x40200000) = 0x00000001;//enable eadc
	   if(rdata%4 == 0)
	     {*(volatile unsigned int *)(rdata) = 0x55667788;}//edac_data
	   else if (rdata%4 == 2)
	   	 {*(volatile unsigned short *)(rdata) = 0x5566;}//edac_data
	   else 
	     {*(volatile unsigned char *)(rdata) = 0x55;}//edac_data
	   
	   *(volatile unsigned int *)(0x40200038) = 0x0;//BC中断状态寄存器清零
	}



     //中断号写入中断结束寄存器(如果不写入，无法接收新中�?
    mprintf(" FIQ interrupt finished! \n");
    gic_eoi_irq(fiq_num);

}

void apb_timer1_int_service()
		{
		u32 rdata; 
		mprintf("!-apb_timer1_int_service begin-! \n");
		rdata = r32(TIMER1_BASE_ADDR+0x10); 
		mprintf("Timer1IntStatus is 0x%x \n",rdata);  
		 
		rdata = r32(TIMER1_BASE_ADDR+0xa0); 
		mprintf("TimersIntStatus is 0x%x \n",rdata);   
		 
		 
		rdata = r32(TIMER1_BASE_ADDR+0x0c);///Timer1EOI
		
		rdata = r32(TIMER1_BASE_ADDR+0x10); 
		mprintf("Timer1IntStatus is 0x%x \n",rdata);  
		 
		rdata = r32(TIMER1_BASE_ADDR+0xa0); 
		mprintf("TimersIntStatus is 0x%x \n",rdata);   
		 
		mprintf("!-apb_timer1_int_service finished-! \n");   
		}






void do_undefined_instruction()
		{
		//u32 udf_instr_addr;
		u32 udf_addr;
			mprintf("!-Undefined instruction acknowledged-! \n");
			//udf_instr_addr=udf_instr_pc();
			__asm__ __volatile__(
			"sub %0, r4, #4\n"
			: "+r"(udf_addr)
			:
			:"memory");      
			mprintf("Address of undefined instruction is 0x%x \n",udf_addr);   
		}

void do_software_interrupt()
		{   
			u32 swi_int_addr,swi_int_num;
			mprintf("!--Software interrupt acknowledged--!\n");

			__asm__ __volatile__(
			"sub %0, r4, #4\n"
			"ldr r0, [r4, #-4]\n"  //LR - 4 为指令" swi xxx" 的地址，低24位是软件中断号
			"bic  r0, r0, #0xFF000000\n"  // 取得ARM指令24位立即数,根据立即数进行具体的中断响应  
			"mov %1, r0\n"
			: "+r" (swi_int_addr),"+r" (swi_int_num)
			:
			:"memory");
		
			mprintf("SWI address is 0x%x, Software interrupt number is %d / 0x%x\n",swi_int_addr,swi_int_num,swi_int_num);
		}

void do_prefetch_abort(void)
		{
			u32 cpsr_prefetch_abort,pre_abr_addr;
			//w32(0x40009cfc,0xeb00031a);
			//w32(0x4000a758,0xe28db004);
			//w32(0x4000a754,0x12334566);
			//w32(0x4000a758,0xacddfddd);
			//dma_cache_flush();
			//  irq_num=gic_get_irq_number();
			mprintf("!-Prefetch_abort  Acknowledged-! \n");

		__asm__ __volatile__(
						"MRS r0, CPSR\n"
						"MOV %0, r0\n"  
						:"+r"(cpsr_prefetch_abort)  
						:
						: "r0"); 
			mprintf("cpsr-prefetch_abort is 0x%x\n",cpsr_prefetch_abort);

		__asm__ __volatile__(
			"sub %0, r4, #4\n"
			: "+r"(pre_abr_addr)
			:
			:"memory");
		mprintf("Address of the prefetch-abort-instruction is 0x%x \n", pre_abr_addr);
			}

void do_data_abort(void)
		{
			u32 rdata1;
			u32 rdata2;
			u32 cpsr_data_abort,data_abort_addr;
		//	print2("\r\n!-Data_abort  Acknowledged-! \n");

		/*****TODO********/
	//	read double_err_ctrl_reg:0xA00E_0000 to judge err type
		rdata1 = r32(0xA00E0000); 
		if( (rdata1&0x1) != 0x0)	//sdram_twobits_err
		{
			
		}
		if( (rdata1&0x2) != 0x0)	//spw3_tx_twobit
		{
			
		}
		if( (rdata1&0x4) != 0x0)	//spw3_rx_twobit
		{
			
		}
		if( (rdata1&0x8) != 0x0)	//spw2_tx_twobit
		{
			
		}
		if( (rdata1&0x10) != 0x0)	//spw2_rx_twobit
		{
			
		}
		if( (rdata1&0x200) != 0x0)	//b15530_tx_twobit
		{
			
		}
		if( (rdata1&0x400) != 0x0)	//b15531_tx_twobit
		{
			
		}
		if( (rdata1&0x800) != 0x0)	//emif_multi_err
		{
			
		}
		if( (rdata1&0x1000) != 0x0)	//sram_twobit_err
		{
			sram_duocuo_irq_handler();
		}

			// rdata1=r32(SDRAM_EDAC_CTRL);
			// rdata2=rdata1&0x1;
			// w32(SDRAM_EDAC_CTRL,rdata2);
			// mprintf("-edac_ctrl_reg is 0x%x\n",rdata1); 
			// w32(r32(SDRAM_TWOBITS_ERR_ADDR),r32(SDRAM_TWOBITS_ERR_DATA));
			// rdata1=r32(SDRAM_EDAC_CTRL);
			// rdata2=rdata1|0xF00;
			// w32(SDRAM_EDAC_CTRL,rdata2);
			

		/* 	__asm__ __volatile__(
						"MRS r0, CPSR\n"
						"MOV %0, r0\n"  
						:"+r"(cpsr_data_abort)  
						:
						: "r0"); 
			mprintf("cpsr_data-abort is 0x%x\n",cpsr_data_abort);

			__asm__ __volatile__(
			"sub %0, r4, #8\n"
			: "+r"(data_abort_addr)
			:
			:"memory");
		mprintf("Address of the data-abort-instruction is 0x%x\n",data_abort_addr); */
			}
void do_not_used(void)
{

    mprintf("!-not_used interrupt Acknowledged-! \n");
   
	}




